# Technical Demo - Quick Reference 

## dbt Setup Commands

### Environment Setup
```bash
# Navigate to dbt project
cd application/dbt

# Activate AWS profile
aws-vault exec iptiq-dev

# Automated setup (recommended)
./setup_local_dev.sh
source activate_dbt.sh

# Manual setup alternative
python3 -m venv dbt_env && source dbt_env/bin/activate
pip install -r requirements.txt && dbt deps
```

### Required Environment Variables
```bash
export DBT_S3_STAGING_DIR=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/athena_query_results/dbt_workgroup/price_crawler/dbt_temp
export DBT_S3_DATA_DIR=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/target/price_crawler/dbt
export DBT_ATHENA_WORKGROUP=data_engineering_workgroup
export USER=your_username
export DBT_S3_ACL=bucket-owner-full-control
export DBT_S3_SSEKMSID='acb2aaab-65c0-4b73-866a-457796a8fc34'
export DBT_S3_SERVER_SIDE_ENCRYPTION=aws:kms
```

### Essential dbt Commands
```bash
# Test connection
dbt debug

# Run models
dbt run --profile personal_schema                                  # All models
dbt run --select model_name --profile personal_schema              # Specific model
dbt run --select +model_name+ --profile personal_schema            # With dependencies

# With date variables
dbt run --select model_name --vars '{"crawler_run_date": "2024-01-15"}' --profile personal_schema

#example models
dbt run --select stg_check24_building_insurance_pricing_data --vars '{"crawler_run_date": "2024-03-06"}' --profile personal_schema                
dbt run --select stg_check24_building_insurance_pricing_data --vars '{"crawler_run_date": "2025-07-08"}' --profile personal_schema

# Testing and documentation
dbt test --profile personal_schema                                 # Run tests
dbt docs generate && dbt docs serve --profile personal_schema      # Generate docs

# Full refresh
dbt run --full-refresh --profile personal_schema                   # Full refresh
```

## Iceberg Table DDL Examples

### Basic Table Creation
```sql
CREATE TABLE price_crawler.insurance_pricing_iceberg (
    provider VARCHAR(100),
    product_name VARCHAR(200),
    price DECIMAL(10,2),
    coverage_details JSON,
    crawler_run_date DATE
)
USING ICEBERG
PARTITIONED BY (crawler_run_date)
LOCATION 's3://your-bucket/iceberg-tables/insurance_pricing/'
TBLPROPERTIES (
    'table_type' = 'ICEBERG',
    'format' = 'PARQUET',
    'write_compression' = 'SNAPPY'
);
```

### Advanced Configuration
```sql
CREATE TABLE price_crawler.motor_insurance_advanced (
    quote_id BIGINT,
    provider STRING,
    premium DECIMAL(12,2),
    coverage_json STRING,
    created_at TIMESTAMP,
    crawler_run_date DATE
)
USING ICEBERG
PARTITIONED BY (days(created_at), crawler_run_date)
LOCATION 's3://your-bucket/iceberg-tables/motor_insurance/'
TBLPROPERTIES (
    'optimize.rewrite.delete-file-threshold' = '2',
    'commit.retry.num-retries' = '3',
    'vacuum.max-snapshot-age-seconds' = '604800000'
);
```

## Iceberg Operations

### CRUD Operations
```sql
-- Update
UPDATE "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data" 
SET price = price * 1.05 
WHERE  crawler_run_date = DATE'2025-07-08' AND provider = 'Neodigital';

-- Delete partition
DELETE FROM "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data" 
WHERE crawler_run_date = DATE'2025-07-08' AND provider = 'Neodigital';

-- Merge (upsert)
MERGE INTO "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data" AS target
USING (SELECT * FROM new_pricing_data) AS source
ON target.provider = source.provider AND target.product_name = source.product_name
WHEN MATCHED THEN UPDATE SET price = source.price
WHEN NOT MATCHED THEN INSERT *;
```

### Time Travel Queries
```sql
-- Query as of timestamp
SELECT *
FROM "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data"
FOR TIMESTAMP AS OF TIMESTAMP '2025-07-22 19:30:18.003'
WHERE crawler_run_date = DATE '2025-07-08'
  AND provider = 'Neodigital';

-- Query specific snapshot
SELECT *
FROM "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data"
FOR VERSION AS OF 8884269075431931422
WHERE crawler_run_date = DATE '2025-07-08'
  AND provider = 'Neodigital'  
```

### Schema Evolution
```sql
-- Add columns
ALTER TABLE dinesh_nallabothula.stg_check24_building_insurance_pricing_data 
ADD COLUMNS (customer_rating DECIMAL(3,2));

-- Change column type
ALTER TABLE dinesh_nallabothula.stg_check24_building_insurance_pricing_data 
CHANGE customer_rating customer_rating DECIMAL(15,2);

-- Rename column
ALTER TABLE dinesh_nallabothula.stg_check24_building_insurance_pricing_data 
CHANGE COLUMN provider provider_name STRING;
```

### Optimization
```sql
-- Clean old snapshots
VACUUM dinesh_nallabothula.stg_check24_building_insurance_pricing_data;

-- Rewrite for better compression
OPTIMIZE dinesh_nallabothula.stg_check24_building_insurance_pricing_data 
REWRITE DATA USING BIN_PACK;
```

## Migration Checklist

### dbt Migration Steps
- [ ] Initialize dbt project with `dbt init`
- [ ] Configure `profiles.yml` with Athena connection
- [ ] Set up environment variables
- [ ] Create source definitions in `models.yml`
- [ ] Convert SQL scripts to dbt models
- [ ] Add Iceberg/Hive configurations
- [ ] Implement custom macros
- [ ] Add data quality tests
- [ ] Update Airflow DAGs to call dbt
- [ ] Test end-to-end pipeline

### Iceberg Adoption Checklist
- [ ] Assess data volume (>10GB recommended)
- [ ] Identify update/delete patterns
- [ ] Evaluate schema evolution needs
- [ ] Check concurrent access requirements
- [ ] Plan migration strategy
- [ ] Set up table properties
- [ ] Implement optimization schedule
- [ ] Monitor performance and costs

## Decision Framework

### Use Iceberg When:
- Data size > 10GB
- Frequent updates/deletes
- Schema evolution needed
- Multiple concurrent writers
- Time travel queries required
- ACID transactions important

### Stick with Parquet When:
- Small datasets (<1GB)
- Append-only patterns
- Simple, static schemas
- Single writer scenarios
- Cost is primary concern
- Team lacks Iceberg expertise

## Troubleshooting

### Common dbt Issues
```bash
# Connection problems
aws sts get-caller-identity    # Check AWS credentials
dbt debug                      # Test dbt connection

# Permission issues
# Ensure S3 bucket permissions and KMS key access
# Verify Athena workgroup permissions

# Model compilation errors
dbt compile --select model_name  # Check compiled SQL
```

### Common Iceberg Issues
```sql
-- View snapshots
SELECT * FROM "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data$snapshots";

-- Check files
SELECT * FROM "dinesh_nallabothula"."stg_check24_building_insurance_pricing_data$files";
```

## Performance Tips

### dbt Optimization
- Use `--select` for targeted runs
- Leverage incremental models for large datasets
- Implement proper testing strategy
- Use macros for reusable logic
- Monitor query performance in Athena

### Iceberg Optimization
- Partition on frequently filtered columns
- Set appropriate file size thresholds
- Regular OPTIMIZE and VACUUM operations
- Monitor metadata overhead
- Use columnar formats (Parquet)

## Key Metrics to Track

### dbt Migration Success
- Development velocity (time to implement new models)
- Data quality incidents (before/after)
- Documentation coverage
- Test coverage percentage
- Developer satisfaction scores

### Iceberg Performance
- Query performance (before/after)
- Storage costs (data + metadata)
- Update operation speed
- Concurrent operation success rate
- Metadata operation costs
