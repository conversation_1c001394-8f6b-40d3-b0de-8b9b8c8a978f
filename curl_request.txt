curl -X POST 'https://en.comparis.ch/autoversicherung/api/v2/input/saveinput' \
  --max-time 60 \
  --proxy 'http://brd-customer-hl_8a8ea9f1-zone-comparis_web_scraper-country-ch:<EMAIL>:33335' \
  --cacert '/Users/<USER>/Downloads/brightdata_proxy_ca/New SSL certifcate - MUST BE USED WITH PORT 33335/BrightData SSL certificate (port 33335).crt' \
  -H 'accept: */*' \
  -H 'accept-language: en-US,en;q=0.9' \
  -H 'content-type: application/json' \
  -H 'user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36' \
  -d '{
    "Header": {
      "Language": "en"
    },
    "UserEmail": "",
    "OptimatisToggleState": true,
    "InsuranceStartDate": "2025-10-03T21:22:58.000Z",
    "CarInput": {
      "ImmatriculationMonth": 7,
      "ImmatriculationYear": 2025,
      "SearchMode": 3,
      "CarMake": 169821,
      "CarModelSeries": 170474,
      "CarModel": 205387,
      "CarGearType": 180007,
      "CarType": 102228316,
      "CarEquipmentPrice": 2000,
      "YearOfPurchase": 2024,
      "IsLeased": true,
      "KmPerYear": 20000,
      "CarUsage": 1,
      "CarGarage": 1,
      "CarRegistrationCanton": "ZH",
      "CarTypeCertificate": "1SC857",
      "LicenseCanton": "",
      "LicenseNumber": ""
    },
    "Driver": {
      "BirthDate": "1980-07-03T00:00:00.000Z",
      "Gender": 1,
      "TownId": "22078",
      "Nationality": "CH",
      "ResidencePermit": null,
      "IsDriverInsuranceTaker": true,
      "LicenseDate": "2013-07-03T00:00:00.000Z",
      "IsOtherDriverUnder25": false,
      "CurrentProvider": 99999
    },
    "Coverages": {
      "CoverageType": 3,
      "RetentionPartialCover": 0,
      "RetentionCollisionCascoCover": 500,
      "HasBonusProtection": true,
      "HasParkingDamage": true,
      "HasParkingDamageUnlimited": true,
      "HasPersonalEffects": true,
      "HasPassengerAccident": false,
      "WantsPreselection": true,
      "HasGrossNegligence": true,
      "HasAssistance": false
    },
    "ClaimsAndConvictionsQuestions": {
      "HasLiabilityDamage": false,
      "HasCascoAndParkingDamage": false,
      "HasDrivingLicenseSuspension": false,
      "HasImprisonmentRecords": false,
      "HasRequestRejected": false,
      "HasTerminatedByInsurance": false
    }
  }'