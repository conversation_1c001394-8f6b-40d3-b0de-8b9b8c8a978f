connections:
  bright_data_webscraper:
    type: generic
    host: **************
    port: 33335
    login: brd-customer-hl_8a8ea9f1-zone-comparis_web_scraper-country-ch
    description: Bright data web scraper proxy server connection details


environment: local
airflowConfigMapName: airflow-common
airflowSecretsName: airflow

image:
  repository: datalake-airflow
  pullPolicy: IfNotPresent
  tag: latest

resources:
  requests:
    cpu: 200m
    memory: 256Mi
  limits:
    cpu: 500m
    memory: 512Mi

variables:
  athena:
    kms_key_id: "/dev/datalake/athena/kms_key_id"
  check24_content_insurance_scrapper:
    input_parameters_path: "input_parameters/dataforcontentcrawler.csv"
    max_concurrent_requests: 10
    max_retry_count: 10
    requests_batch_size: 50
    html_s3_prefix: "raw/crawlers/check24/content/html_responses"
    athena_sink:
      raw_database: "databucket_raw"
      target_database: "databucket_target"
      table: "check24_content_insurance_pricing_data"
      athena_kms_key_id: "/dev/datalake/athena/kms_key_id"
  check24_building_insurance_scrapper:
    input_parameters_path: "input_parameters/Check24CrawlerBasket.csv"
    max_concurrent_requests: 5
    max_retry_count: 10
    requests_batch_size: 50
    html_s3_prefix: "/raw/crawlers/check24/building/html_responses/"
    athena_sink:
      raw_database: "databucket_raw"
      table: "check24_building_insurance_pricing_data"
      athena_kms_key_id: "/dev/datalake/athena/kms_key_id"
  comparis_motor_insurance_scraper:
    max_retries: 10
    polling_interval: 4
    polling_timeout: 90
    max_concurrent_requests: 10
    chunk_size: 100
    input_parameters_path: "input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv"
    aggregation_file_path: "input_parameters/comparis/comparis_motor/config/ComparisEFAG_test.csv"
    athena_sink:
      raw_database: "databucket_raw"
      comparis_table: "comparis_motor_insurance_pricing_data"
      turing_table: "turing_efag_pricing_data"
      athena_kms_key_id: "/dev/datalake/athena/kms_key_id"
  price_crawler_dbt_config:
    project_dir: "/opt/airflow/dags/price-crawler_dbt"
    athena_workgroup: "data_engineering_workgroup"
    dbt_s3_staging_dir: "s3://eu-central-1-912399619264-dev-datalake-infra-base-data/athena_query_results/dbt_workgroup/price_crawler/dbt_temp"
    dbt_s3_data_dir: "s3://eu-central-1-912399619264-dev-datalake-infra-base-data/target/price_crawler/dbt"
    dbt_user: "dbt"
