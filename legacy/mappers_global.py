def getPricedElements(product_id):
    pricedElements_HEMS = {'ContentsAtHome': 242, 'ContentsAway': 243, 'BuildingGlass': 264, 'FurnitureGlass': 265,
                           'PersonalLiability': 257, 'PL LicensedTPV': 258, 'PL Tenant': 259}
    pricedElements_EFAG = {'MTPL': 267, 'MOD': 408, 'GlassWindow': 409, 'GlassPlus': 410}
    pricedElements_INDEPM = {'MTPL': 267, 'MTPL Own Vehicle': 412, 'MOD': 408, 'GlassWindow': 409, 'Passenger': 411}
    pricedElements_check_building = {'GlassAddon': 413, 'Burglary': 414, 'Animal': 415, 'Building': 416,
                                     'Waste Water': 417}
    pricedElements_INDEPHH = {'Building': 401, 'Glass': 402, 'Content': 403, 'Electronics': 404, 'ContentAllrisk': 405,
                              'PL Private': 406, 'PL Tenant': 407}  # ! 416 is building and elementary

    return {'HEMSAKER': pricedElements_HEMS,
            'CHMOTOR': pricedElements_EFAG,
            'CHMOTOR_COMPANIES': pricedElements_EFAG,
            'CHMOTOR_LEASING': pricedElements_EFAG,
            'CHMOTOR_COMPANIES_LEASING': pricedElements_EFAG,
            'INDEPENDER_MOTOR': pricedElements_INDEPM,
            'CHECK24_BUILDING': pricedElements_check_building,
            'INDEPENDER_HOUSEHOLD': pricedElements_INDEPHH}.get(product_id)
