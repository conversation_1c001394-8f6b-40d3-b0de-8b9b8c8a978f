# price-crawler

Repo for crawling price info from external websites

## Design

### Check24 Crawlers Design

![check24_crawlers_design.png](images%2Fcheck24_crawlers_design.png)

# Comparis Motor Insurance Crawler

## Overview
This Comparis Motor Scraper provides a tool for crawling motor insurance pricing data from en.comparis.ch. It processes input parameters in batches and saves the results to athena/csv file, making it efficient for large-scale data collection.

## Comparis Motor Crawler Design
![comparis_motor_crawler_design.png](images%2Fcomparis_motor_crawler_design.png)

## Prerequisites
Prerequisites for running Comparis Motor Insurance Crawler locally

### System Requirements
- Python 3.10 or higher
- MacOS or Linux environment
- Valid Brightdata proxy credentials

### Python Dependencies
```bash
pip install httpx pandas pydantic asyncio
```

### Project Structure

```plaintext
price-crawler/
├── application/
│   └── airflow/
│       └── dags/
│           ├── comparis_scraper/              # Core scraper package
│           │   ├── models/                    # Data models
│           │   ├── scrapers/                  # Scraper implementations
│           │   └── transformers/              # Data transformation logic
│           ├── pricing_analysts_adhoc_scripts/
│           │   └── comparis_motor_insurance_crawler.py  # Main crawler script
│           └── input_parameters/
│               └── comparis/
│                   └── comparis_motor/
│                       └── ComparisEFAG_test.csv       # Input parameters file
```

### Input File Format
The crawler expects a CSV file with specific columns. Here are the key fields:

## Running the Crawler

### Basic Usage
```bash
cd /Users/<USER>/repos/price-crawler/application/airflow/dags/pricing_analysts_adhoc_scripts

python comparis_motor_insurance_crawler.py \
  --input ../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv \
  --output ./output_data/comparis_results.csv
 ```

 ### Command Line Arguments

- --input : Path to input CSV file (default: ../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv)
- --output : Path to output CSV file (default: ./output_data/output_data_comparis_motor_[timestamp].csv)
- --max-concurrent : Maximum number of concurrent requests (default: 10)
- --chunk-size : Number of records to process in each chunk (default: 100)

### Example with Custom Parameters

```bash
python comparis_motor_insurance_crawler.py \
  --input ../input_parameters/comparis/comparis_motor/my_input.csv \
  --output ./output_data/my_results.csv \
  --max-concurrent 5 \
  --chunk-size 50
 ```

 ## Output Format
The crawler generates a CSV file containing:

- Insurance provider details
- Premium amounts
- Coverage details
- Deductibles
- Additional options and their costs

## Troubleshooting

### Common Issues
1. Connection Errors

   - Check proxy configuration
   - Verify network connectivity
   - Ensure Brightdata credentials are valid
2. Invalid Input Data

   - Verify all required columns are present
   - Check data format matches expectations
3. Rate Limiting

   - Reduce max-concurrent value
   - Increase polling interval
   - Check proxy status
### Logging
The crawler provides detailed logging information:

- Progress updates
- Error messages
- Processing statistics
## Best Practices
1. Start with small batches to verify configuration
2. Monitor memory usage with large datasets
3. Keep input files well-organized
4. Use meaningful output filenames
