repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        exclude: ^.gitlab
  - repo: https://github.com/pycqa/flake8
    rev: 3.8.4
    hooks:
      - id: flake8
        args:
          - --exit-zero
          - --config
          - application/airflow/.flake8
          - --black-config
          - application/airflow/pyproject.toml
        additional_dependencies:
          - flake8-mypy
          - flake8-black
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        args:
          - --safe
          - --config
          - application/airflow/pyproject.toml
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)
        files: "\\.(py)$"
        args:
          - application/airflow/pyproject.toml
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.7.1.1
    hooks:
      - id: shellcheck
        args:
          - --source-path=SCRIPTDIR
          - --external-sources
  - repo: https://github.com/gruntwork-io/pre-commit
    rev: v0.1.12
    hooks:
      - id: terraform-fmt
      # requires setting AWS_DEFAULT_REGION for modules
      # see https://github.com/hashicorp/terraform/issues/21408
      # alternatively, exclude the modules from this
      - id: terraform-validate
