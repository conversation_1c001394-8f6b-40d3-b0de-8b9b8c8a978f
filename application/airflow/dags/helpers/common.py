# -*- coding: utf-8 -*-
"""Common utility function library for Airflow DAGs"""

import logging
import os
from functools import lru_cache

from airflow.models import Variable

DATALAKE_ENVIRONMENT = os.environ.get("DATALAKE_ENVIRONMENT")
DAGS_ROOT = "/opt/airflow/dags"

logger = logging.getLogger(__file__)


@lru_cache
def get_dbt_env_var() -> dict:
    """
    Returns the dbt environment variables
    """
    dbt_config = Variable.get("price_crawler_dbt_config", deserialize_json=True)
    return {
        "DBT_S3_STAGING_DIR": dbt_config.get("dbt_s3_staging_dir"),
        "DBT_S3_DATA_DIR": dbt_config.get("dbt_s3_data_dir"),
        "DBT_ATHENA_WORKGROUP": dbt_config.get("athena_workgroup"),
        "USER": dbt_config.get("dbt_user"),
        "AWS_REGION": "eu-central-1",
    }


def get_dbt_project_dir() -> str:
    """
    Returns the dbt project directory
    """
    dbt_config = Variable.get("price_crawler_dbt_config", deserialize_json=True)
    if DATALAKE_ENVIRONMENT == "local":
        if os.environ.get("PRICE_CRAWLER_PROJECT_PATH") is not None:
            return os.path.join(
                f'{os.environ.get("PRICE_CRAWLER_PROJECT_PATH")}/application/',
                dbt_config.get("project_dir"),
            )
        else:
            return os.path.join(DAGS_ROOT, dbt_config.get("project_dir"))
    else:
        return dbt_config.get("project_dir")


def run_dbt_command(partition_date: str, dbt_cmd: str = None) -> None:
    """Execute a dbt command with the specified partition date and command.

    Args:
        partition_date (str): The partition date to use for the dbt command execution
        dbt_cmd (str, optional): The dbt command to execute. Defaults to None.

    Returns:
        None
    """
    import shutil
    import uuid
    from datetime import date

    from quantum_data_pipeline.dbt.hooks.dbt_hook import DbtCliHook
    from quantum_data_pipeline.utils.common import connect_to_aws_dev_from_local, get_execution_date

    connect_to_aws_dev_from_local()

    # Append all the OS env vars (including the aws tokens)
    os_env = {k: v for k, v in os.environ.items()}
    dbt_env_var = get_dbt_env_var()
    os_env.update(dbt_env_var)
    dbt_hook = DbtCliHook(
        dir=get_dbt_project_dir(),
        env=os_env,
        dbt_bin=shutil.which("dbt"),
        vars={
            "iptiq_load_id": str(uuid.uuid4()),
            "iptiq_load_date": str(date.today()),
            "iptiq_execution_date": get_execution_date(),
            "crawler_run_date": partition_date,
        },
    )
    dbt_hook.run_cli(dbt_cmd)
