import asyncio
import json
import logging
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlencode

import httpx
import pandas as pd
from attr import NOTHING
from check24_crawlers.check24_base_scrapper import Check24BaseScraper
from lxml import html
from price_parser import parse_price

logger = logging.getLogger(__name__)


@dataclass
class Check24ContentInsuranceScraperArguments:
    """
    Represents the arguments for the check24 personal household contents insurance scraper.

    Args:
        csv (Path): Path to the CSV file to parse.
        squaremeter (str): Square meter of the home.
        zipcode (str): ZIP code.
        birthdate (str): Birthdate of the policyholder.
        public_service (str): Flag indicating if the policyholder is in public service.
        insurancesum (str): Insurance sum.
        costsharing (str): Deductible.
        bike_insurance_sum (str): Bike insurance sum.
        risk_address_city (str): City of the risk address.
        module_glass (str): Flag indicating if the glass addon is selected.
        building_type (str): Building type.
        module_elementary (str): Flag indicating if the elementary addon is selected.
        module_elementary_city (str): City.
        module_elementary_street (str): Street name.
        module_elementary_street_number (str): Street number.
        chunk_size (int): Number of parameters to process for each output file.
        md5_hash (str): md5 hash of the input parameters file.
    """

    csv: Path = field(metadata={"description": "Path to the CSV file to parse"})
    squaremeter: Optional[str] = field(default=None, metadata={"description": "Square meter of the home"})
    zipcode: Optional[str] = field(
        default=None,
        metadata={
            "description": "ZIP code",
        },
    )
    birthdate: Optional[str] = field(
        default=None,
        metadata={
            "description": "Birthdate of the policy holder",
        },
    )
    public_service: Optional[str] = field(
        default=None,
        metadata={
            "description": "Flag indicating if the policy holder is in public service",
        },
    )
    insurancesum: Optional[str] = field(default=None, metadata={"description": "Insurance sum"})
    costsharing: Optional[str] = field(
        default=None,
        metadata={
            "description": "Deductible",
        },
    )
    bike_insurance_sum: Optional[str] = field(
        default=None,
        metadata={
            "description": "Bike insurance sum",
        },
    )
    risk_address_city: Optional[str] = field(default=None, metadata={"description": "City of the risk address"})
    module_glass: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the glass addon is selected"}
    )
    building_type: Optional[str] = field(
        default=None,
        metadata={
            "description": "Building type",
        },
    )
    module_elementary: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the elementary addon is selected"}
    )
    module_elementary_city: Optional[str] = field(
        default=None,
        metadata={
            "description": "City",
        },
    )
    module_elementary_street: Optional[str] = field(default=None, metadata={"description": "Street name"})
    module_elementary_street_number: Optional[str] = field(default=None, metadata={"description": "Street number"})
    chunk_size: int = field(
        metadata={"description": "Number of parameters to process for each output file."},
        default=1000,
    )
    md5_hash: str = field(default=None, metadata={"description": "md5 hash of the input parameters file."})

    def __post_init__(self):
        # Convert empty strings to None
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, str) and field_value.strip() == "":
                setattr(self, field_name, None)


@dataclass
class Check24ContentInsuranceScraper(Check24BaseScraper):
    """
    Class for scraping aggregators in home content insurance.

    Methods:
        parse_birthdate(date_str: str) -> str:
            Parse and reformat the birthdate.

        csv_parser(csv_path: Path) -> List[Dict]:
            Parse CSV file and return a list of dictionaries.

        create_url(args: Dict, data_dict: Dict = None) -> str:
            Create the URL for scraping.

        fetch_web_data(url: str) -> List[Dict]:
            Fetch web data from the specified URL.

        main(args: Namespace):
            Main function to orchestrate the scraping process.
    """

    __service_name__ = "check24_content_insurance_scrapper"

    base_url: str = "https://hausratversicherungen.check24.de/hausrat/vergleichsergebnis/"
    semaphore: asyncio.Semaphore = asyncio.Semaphore(5)
    max_retries: int = 10
    bucket_name: Optional[str] = None
    html_s3_prefix: Optional[str] = None

    def __post_init__(self):
        super().__init__(
            semaphore=self.semaphore,
            max_retries=self.max_retries,
            bucket_name=self.bucket_name,
            html_s3_prefix=self.html_s3_prefix,
        )

    @staticmethod
    def parse_birthdate(date_str: str) -> str:
        """Parse and reformat the birthdate.

        Args:
            date_str (str): The birthdate string in the format "DD.MM.YY".

        Returns:
            str: The reformatted birthdate string in the format "DD.MM.YYYY".
        """
        date_parts = date_str.split(".")
        year = int(date_parts[-1])
        date_parts[-1] = "20" + date_parts[-1] if year <= ((datetime.now().year + 1) % 100) else "19" + date_parts[-1]
        return ".".join(date_parts)

    def df_transformation(self, chunk: pd.DataFrame) -> pd.DataFrame:
        """
        Transform the input DataFrame.

        Parameters:
        - chunk (DataFrame): chuck of parameters from dataframe

        """
        try:
            chunk["birthdate"] = chunk["birthdate"].apply(self.parse_birthdate)
            return chunk
        except Exception as e:
            logger.error("Error parsing the input parameters dataframe")
            logger.error("Got the following error %s" % e)
            raise e

    def create_url(self, args: Check24ContentInsuranceScraperArguments, data_dict: Dict = None) -> str:
        """Create the URL for scraping."""
        try:
            params = {
                "sortfield": "price",
                "sortorder": "asc",
                "paymentperiod": "year",
                "contractperiod": "1",
                "min_stars": "0",
            }

            # List of parameters to check in args and dict
            arg_params = [
                "squaremeter",
                "zipcode",
                "birthdate",
                "public_service",
                "insurancesum",
                "costsharing",
                "bike_insurance_sum",
                "module_glass",
                "building_type",
                "module_elementary",
                "module_elementary_city",
                "module_elementary_street",
                "module_elementary_street_number",
            ]

            # Update params with values from args or dict
            for param in arg_params:
                params[param] = getattr(args, param, None) or data_dict.get(param)

            # Special handling for bike_insurance_sum if it's 0
            if args.bike_insurance_sum == 0:
                params.pop("bike_insurance_sum", None)

            # Encode parameters and append to the base URL
            query_string = urlencode(params)
            return f"{self.base_url}?{query_string}"
        except Exception as e:
            logger.debug(f"Error creating the url for below params: {e}")
            logger.debug(f"params: {data_dict}")

    @staticmethod
    def _handle_failed_response(status_reason: str = "Unknown error occurred") -> List[Dict[str, str]]:
        """
        Handle failed response by returning a default dictionary.

        Args:
            status_reason (str, optional): Reason for the failure. Defaults to "Unknown error occurred".

        Returns:
            List[Dict[str, str]]: List containing a default dictionary representing failure.
        """
        return [
            {
                "provider": None,
                "tariff_name": None,
                "price": None,
                "currency": None,
                "recommendation": None,
                "grade": None,
                "status": "Failed",
                "status_reason": status_reason,
            }
        ]

    @staticmethod
    async def parser_logic(response: httpx.Response) -> List[Dict[str, str]]:
        """
        Parse the HTML content from the response and extract insurance tariff information.

        This method extracts tariff details including provider name, tariff name, price,
        currency, grade, and recommendation from the JSON data embedded in the HTML.

        Args:
            response (Response): The HTTP response object containing the HTML content.

        Returns:
            List[Dict[str, str]]: A list of dictionaries, each containing
            information about a single tariff. If parsing fails, returns a list with a
            single dictionary containing error information.

        Raises:
            json.JSONDecodeError: If the JSON data in the script tag is malformed.
            KeyError: If expected keys are missing in the JSON structure.
            AttributeError: If the response lacks expected attributes or methods.

        Example return value:
            [
                {
                    "provider": "FRIDAY Versicherung",
                    "tariff_name": "Balance",
                    "price": "5.65",
                    "currency": "€",
                    "grade": "1,2",
                    "recommendation": "Preis-Leistungs-",
                    "status": "success",
                    "status_reason": "successfully crawled the url",
                },
                # ... more tariffs ...
            ]
        """
        html_content = response.content

        tree = html.fromstring(html_content)

        script_tag = tree.xpath('//script[@data-json and @type="text/json"]')

        if not script_tag:
            return Check24ContentInsuranceScraper._handle_failed_response("No JSON data found in script tag")

        try:
            json_data = json.loads(script_tag[0].text)

            # Get the list of tariffs
            tariffs = json_data["resultList"]["tariffsData"]["tariffs"]

            # Extract the required information for each tariff
            output_array = list()
            for tariff in tariffs:
                # Append extracted data to output array
                output_array.append(
                    {
                        "provider": tariff["gradeDetails"]["providerName"],
                        "tariff_name": tariff["name"],
                        "price": str(tariff["price"].get("monthlyPrice", NOTHING))
                        .replace(",", ".")
                        .replace(r"[^\d.]", ""),
                        "currency": parse_price(tariff["price"].get("periodSaving", NOTHING)).currency,
                        "grade": tariff["gradeDetails"]["grade"].get("result", "Nothing for this product."),
                        "recommendation": tariff["banner"].get("title1Text", "Nothing for this product."),
                        "status": "success",
                        "status_reason": "successfully crawled the url",
                    }
                )
            return output_array
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            msg = f"An error occurred extracting the data from content: {e!r}"
            return Check24ContentInsuranceScraper._handle_failed_response(msg)

    async def fetch_web_data(self, session: httpx.AsyncClient, full_url: str) -> List[Dict[str, str]]:
        """
        Fetch web data from the specified URL.

        Args:
            session (httpx.AsyncClient): The HTTPX async client session.
            full_url (str): The full URL to fetch data from.

        Returns:
            List[Dict[str, str]]: List of dictionaries containing scraped data.
        """
        response = await self._get(session=session, full_url=full_url, timeout=10)
        if response["status"] == "Failed":
            return Check24ContentInsuranceScraper._handle_failed_response(response["status_reason"])

        return await Check24ContentInsuranceScraper.parser_logic(response["data"])
