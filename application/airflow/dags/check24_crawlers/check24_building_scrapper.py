import asyncio
import logging
from dataclasses import dataclass, field
from datetime import date
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlencode

import httpx
import pandas as pd
from attr import NOTHING
from check24_crawlers.check24_base_scrapper import Check24BaseScraper
from lxml import html
from price_parser import parse_price

logger = logging.getLogger(__name__)


@dataclass
class Check24BuildingInsuranceScraperArguments:
    """
    Represents the arguments for the check24 building insurance scraper.

    Args:
        csv (Path): Path to the CSV file to parse.
        building_standard (str): Building standard.
        zip_code (str): ZIP code.
        fire_zone (str): Fire zone.
        water_zone (str): Water zone.
        sh_zone (str): SH zone.
        building_class (str): Building class.
        prefabricated_house (str): Flag indicating if the house is prefabricated.
        wall_type (str): Wall type.
        roof_type (str): Roof type.
        rented_out (str): Flag indicating if the home is rented out.
        age (str): Age of construction.
        renovated (str): Flag indicating if the home is renovated.
        deductible (str): Deductible amount.
        home_type (str): Home type.
        solar_panels (str): Flag indicating if the home has solar panels.
        floor_heating (str): Flag indicating if the home has ceiling or floor heating.
        massive_outbuildings (str): Flag indicating if there are massive outbuildings.
        surface (str): Home surface.
        cellar (str): Flag indicating if the house has a cellar.
        basement (str): Basement surface.
        commercial (str): Flag indicating if the home is used for commercial purposes.
        premium_frequency (str): Premium frequency.
        street_name (str): Street name.
        street_number (str): Street number.
        module_fire (str): Flag indicating if the fire module shall be included.
        module_water (str): Flag indicating if the water module shall be included.
        module_glass (str): Flag indicating if the glass module shall be included.
        module_elementary (str): Flag indicating if the elementary module shall be included.
        chunk_size (str): Number of parameters to process for each output file.
        md5_hash (str): md5 hash of the input parameters file.
    """

    csv: Path = field(
        metadata={
            "description": "Path to the CSV file to parse.",
        }
    )
    csv: Path
    building_standard: Optional[str] = field(default=None, metadata={"description": "Building standard."})
    zip_code: Optional[str] = field(default=None, metadata={"description": "ZIP code."})
    fire_zone: Optional[str] = field(default=None, metadata={"description": "Fire zone."})
    water_zone: Optional[str] = field(default=None, metadata={"description": "Water zone."})
    sh_zone: Optional[str] = field(default=None, metadata={"description": "SH zone."})
    building_class: Optional[str] = field(default=None, metadata={"description": "Building class."})
    prefabricated_house: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the house is prefabricated."}
    )
    wall_type: Optional[str] = field(default=None, metadata={"description": "Wall type."})
    roof_type: Optional[str] = field(default=None, metadata={"description": "Roof type."})
    rented_out: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the home is rented out."}
    )
    age: Optional[str] = field(default=None, metadata={"description": "Age of construction."})
    renovated: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the home is renovated."}
    )
    deductible: Optional[str] = field(default=None, metadata={"description": "Deductible amount."})
    home_type: Optional[str] = field(default=None, metadata={"description": "Home type."})
    solar_panels: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the home has solar panels."}
    )
    floor_heating: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the home has ceiling or floor heating."}
    )
    massive_outbuildings: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if there are massive outbuildings."}
    )
    surface: Optional[str] = field(default=None, metadata={"description": "Home surface."})
    cellar: Optional[str] = field(default=None, metadata={"description": "Flag indicating if the house has a cellar."})
    basement: Optional[str] = field(default=None, metadata={"description": "Basement surface."})
    commercial: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the home is used for commercial purposes."}
    )
    premium_frequency: Optional[str] = field(default=None, metadata={"description": "Premium frequency."})
    street_name: Optional[str] = field(default=None, metadata={"description": "Street name."})
    street_number: Optional[str] = field(default=None, metadata={"description": "Street number."})
    module_fire: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the fire module shall be included."}
    )
    module_water: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the water module shall be included."}
    )
    module_glass: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the glass module shall be included."}
    )
    module_elementary: Optional[str] = field(
        default=None, metadata={"description": "Flag indicating if the elementary module shall be included."}
    )
    chunk_size: int = field(
        default=1000, metadata={"description": "Number of parameters to process for each output file."}
    )
    md5_hash: str = field(default=None, metadata={"description": "md5 hash of the input parameters file."})

    def __post_init__(self):
        # Convert empty strings to None
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, str) and field_value.strip() == "":
                setattr(self, field_name, None)


@dataclass
class Check24BuildingInsuranceScraper(Check24BaseScraper):
    """
    Class for scraping check24 aggregator in building insurance.

    Methods:
        csv_parser(csv_path: Path) -> List[Dict]:
            Parse CSV file and return a list of dictionaries.

        create_url(args: Check24BuildingInsuranceScraperArguments, data_dict: Dict = None) -> str:
            Create the URL for scraping.

        fetch_web_data(full_url: str) -> List[Dict]:
            Fetch web data from the specified URL.
    """

    __service_name__ = "check24_building_insurance_scrapper"

    base_url: str = "https://wohngebaeude.check24.de/whg/vergleichsergebnis/"
    semaphore: asyncio.Semaphore = asyncio.Semaphore(5)
    max_retries: int = 10
    bucket_name: Optional[str] = None
    html_s3_prefix: Optional[str] = None

    def __post_init__(self):
        super().__init__(
            semaphore=self.semaphore,
            max_retries=self.max_retries,
            bucket_name=self.bucket_name,
            html_s3_prefix=self.html_s3_prefix,
        )

    def df_transformation(self, chunk: pd.DataFrame) -> pd.DataFrame:
        """
        Transform the input DataFrame.

        Parameters:
        - chunk (DataFrame): chuck of parameters from dataframe

        """
        try:

            chunk["streetname"] = chunk["streetname"].apply(lambda x: "Hauptstr." if x == "No Value" else x)
            chunk["housenumber"] = chunk["housenumber"].apply(lambda x: "1" if x == "No Value" else x)

            # Rename columns
            df = chunk.rename(
                columns={
                    "Building standard": "building_standard",
                    "Zip code": "zip_code",
                    "Fire zone": "fire_zone",
                    "Water zone": "water_zone",
                    "SH zone": "sh_zone",
                    "Building Class": "building_class",
                    "Prefabricated House": "prefabricated_house",
                    "Wall type": "wall_type",
                    "Roof type": "roof_type",
                    "Rented out": "rented_out",
                    "Age": "age",
                    "Rennovated": "renovated",
                    "Deductible": "deductible",
                    "Type of home": "home_type",
                    "Solar panel": "solar_panel",
                    "Floor or ceiling heating": "floor_or_ceiling_heating",
                    "Massive outbuildings": "massive_outbuildings",
                    "SqM": "surface",
                    "Basement": "basement",
                    "Commercial (Yes/No)": "commercial",
                    "Prem frequ": "prem_freq",
                    "streetname": "street_name",
                    "housenumber": "street_number",
                    "Addon_wastewater": "module_water",
                    "Addon_Glass": "module_glass",
                    "Addon_Elementary": "module_elementary",
                    "cityname": "risk_address_city",
                }
            )

            return df
        except Exception as e:
            logger.error("Error parsing the input parameters dataframe")
            logger.error("Got the following error %s" % e)
            raise e

    def create_url(self, args: Check24BuildingInsuranceScraperArguments, data_dict: Dict = None) -> str:
        """
        Create a URL for building insurance comparison based on the provided arguments or data dictionary.

        Parameters:
        - args (Check24BuildingInsuranceScraperArguments): The command-line/config arguments.
        - data_dict (dict, optional): Dictionary containing parameters for the url.

        Returns:
        - str: The generated URL for building insurance comparison.
        """
        try:
            params = {
                "parking_position_or_massive_outbuilding": "no",
                "amount_carport_parking_position": "0",
                "amount_underground_parking_position": "0",
                "building_equipment": "normal",
                "min_stars": "0",
                "module_bhh": "no",
                "bhh_own_contribution": "no",
                "bhh_insurance_sum": "5000000",
                "module_blv": "no",
                "blv_own_contribution": "no",
                "amount_upper_floors": "0",
                "structural_restoration_executed": "no",
                "structural_restoration_pipes": "no",
                "structural_restoration_roof": "no",
                "structural_restoration_power_lines": "no",
                "structural_restoration_heating": "no",
                "restoration_heating_year": "0",
                "restoration_core_construction_year": "0",
                "restoration_bathroom_year": "0",
                "roof_stone_copper": "no",
                "roof_stone_pottery_brick": "no",
                "stucco_exotic_wood": "no",
                "sanitation_luxury": "no",
                "floor_stone_parquet_carpet": "no",
                "window_metal_wood": "no",
                "door_exotic_wood": "no",
                "floor_vinyl": "no",
                "window_basic": "no",
                "without_bath": "no",
                "stove_heating": "no",
                "paymentperiod": "year",
                "content_massive_outbuildings": (
                    args.massive_outbuildings
                    if args.massive_outbuildings is not None
                    else data_dict.get("massive_outbuildings")
                ),
                "pump_solar": args.solar_panels if args.solar_panels is not None else data_dict.get("solar_panel"),
                "floor_ceiling_heat": args.floor_heating
                if args.floor_heating is not None
                else data_dict.get("floor_or_ceiling_heating"),
                "risk_address_street": args.street_name
                if args.street_name is not None
                else data_dict.get("street_name"),
                "risk_address_streetnumber": args.street_number
                if args.street_number is not None
                else data_dict.get("street_number"),
                "risk_address_city": args.zip_code if args.zip_code is not None else data_dict.get("risk_address_city"),
                "costsharing": args.deductible if args.deductible is not None else data_dict.get("deductible"),
                "building_type": args.building_standard
                if args.building_standard is not None
                else data_dict.get("home_type"),
                "is_landlord": args.rented_out if args.rented_out is not None else data_dict.get("rented_out"),
                "risk_address_zipcode": args.zip_code if args.zip_code is not None else data_dict.get("zip_code"),
                "year_of_construction": args.age if args.age is not None else data_dict.get("age"),
                "insure_date": date.today().strftime("%d.%m.%Y"),
                "prefab_house": args.prefabricated_house
                if args.prefabricated_house is not None
                else data_dict.get("prefabricated_house"),
                "type_of_construction": args.wall_type if args.wall_type is not None else data_dict.get("wall_type"),
                "type_of_roofing": args.roof_type if args.roof_type is not None else data_dict.get("roof_type"),
                "squaremeter": args.surface if args.surface is not None else data_dict.get("surface"),
                "cellar": args.cellar if args.cellar is not None else data_dict.get("basement"),
                "squaremeter_cellar": args.basement if args.basement is not None else "0",
                "module_glass": args.module_glass if args.module_glass is not None else data_dict.get("module_glass"),
                "module_elementary": args.module_elementary
                if args.module_elementary is not None
                else data_dict.get("module_elementary"),
                "module_water": args.module_water if args.module_water is not None else data_dict.get("module_water"),
            }

            # logger.info(f"Parameters: {params}")
            query_string = urlencode(params)
            return f"{self.base_url}?{query_string}"
        except Exception as e:
            logger.debug(f"Error creating the url for below params: {e}")
            logger.debug(f"params: {data_dict}")

    @staticmethod
    def _handle_failed_response(status_reason: str = "Unknown error occurred") -> List[Dict[str, str]]:
        """
        Handle failed response by returning a default dictionary.

        Args:
            status_reason (str, optional): Reason for the failure. Defaults to "Unknown error occurred".

        Returns:
            List[Dict[str, str]]: List containing a default dictionary representing failure.
        """
        return [
            {
                "provider": None,
                "tariff_name": None,
                "price": None,
                "currency": None,
                "features": None,
                "highlight": None,
                "grade": None,
                "status": "Failed",
                "status_reason": status_reason,
            }
        ]

    async def fetch_web_data(self, session: httpx.AsyncClient, full_url: str) -> List[Dict[str, str]]:
        """
        Fetch web data from the specified URL.

        Args:
            session (httpx.AsyncClient): The HTTPX async client session.
            full_url (str): The full URL to fetch data from.

        Returns:
            List[Dict[str, str]]: List of dictionaries containing scraped data.
        """
        response = await self._get(session=session, full_url=full_url)
        if response["status"] == "Failed":
            return Check24BuildingInsuranceScraper._handle_failed_response(response["status_reason"])

        content_data = await Check24BuildingInsuranceScraper.get_content(response["data"])
        if content_data["status"] == "Failed":
            return Check24BuildingInsuranceScraper._handle_failed_response(content_data["status_reason"])

        try:
            # Extract quote blocks from the HTML content
            quote_blocks = content_data["html_content"].xpath('//div[@class="result_box_inner"]')

            if not quote_blocks:
                return Check24BuildingInsuranceScraper._handle_failed_response(
                    "Oops! Nothing is returned for the input parameters"
                )

            output_array = []

            # Extract eyecatchers and grades from the HTML content
            eyecatchers = content_data["html_content"].xpath('//h1[@class="c24-eyecatcher-headline"]')
            grades = content_data["html_content"].xpath("//grade-item")

            for index, quote_block in enumerate(quote_blocks):
                quote_block_html = html.fromstring(html.tostring(quote_block))

                # Extract provider name, tariff name, and price element
                provider_name = quote_block_html.xpath('//div[@class="provider_name"]')[0]
                tariff_name = quote_block_html.xpath('//div[@class="tariff_name"]')[0]
                price_element_query = quote_block_html.xpath(
                    '//div[contains(@class, "price regular-price-only") or contains(@class, "price rebate-price ")]'
                )
                price_element = price_element_query[0] if price_element_query else NOTHING

                # Parse and extract price information
                if not hasattr(price_element, "text_content"):
                    continue
                price = parse_price(price_element.text_content().strip())

                # Extract eyecatching and grade information
                eyecatching = (
                    eyecatchers[index].text_content()
                    if index < len(eyecatchers) and index < 2 and eyecatchers[index].text_content()
                    else "Nothing for this product."
                )
                grade = grades[index].attrib.get(":value", None) or "Nothing for this product."

                # Extract feature list
                feature_list_elements = quote_block_html.xpath('//div[@class="tariff_features_bullets_box"]//li')
                feature_list_array = [
                    feature_list_element.text_content().replace(
                        feature_list_element.xpath('.//div[@class="tooltip-text"]')[0].text_content(),
                        "",
                    )
                    if feature_list_element.xpath('.//div[@class="tooltip-text"]')
                    else feature_list_element.text_content()
                    for feature_list_element in feature_list_elements
                ]

                # Append extracted data to output array
                output_array.append(
                    {
                        "provider": provider_name.text_content(),
                        "tariff_name": tariff_name.text_content(),
                        "price": str(price.amount),
                        "currency": price.currency,
                        "features": feature_list_array,
                        "highlight": eyecatching,
                        "grade": grade,
                        "status": "success",
                        "status_reason": "successfully crawled the url",
                    }
                )
            return output_array
        except Exception as e:
            msg = f"An error occurred extracting the data from content: {e!r}"
            return Check24BuildingInsuranceScraper._handle_failed_response(msg)
