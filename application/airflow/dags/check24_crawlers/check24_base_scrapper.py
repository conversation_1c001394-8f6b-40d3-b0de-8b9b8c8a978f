import asyncio
import gzip
import hashlib
import logging
import random
import time
from abc import ABC
from datetime import datetime
from io import BytesIO
from typing import Any, AsyncGenerator, Dict, List, Union

import boto3
import httpx
import pandas as pd
from lxml import html
from requests.models import Response

from airflow.utils.file import open_maybe_zipped

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)


class Check24BaseScraper(ABC):
    def __init__(
        self, semaphore: asyncio.Semaphore, max_retries: int = 10, bucket_name: str = None, html_s3_prefix: str = None
    ):
        self.list_of_dictionaries = None
        self.semaphore = semaphore
        self.max_retries = max_retries
        self.s3_client = boto3.client("s3")
        self.bucket_name = bucket_name
        self.html_s3_prefix = html_s3_prefix

    ...

    @staticmethod
    def exponential_backoff(base_delay: float = 10.0, max_delay: float = 25.0, jitter: float = 0.5) -> float:
        """
        Implements exponential backoff with jitter.

        Args:
            base_delay (float, optional): The initial delay. Defaults to 10.0.
            max_delay (float, optional): The maximum delay. Defaults to 25.0.
            jitter (float, optional): The amount of jitter to add to the delay. Defaults to 0.5.

        Returns:
            float: The next delay to wait.
        """
        backoff = min(max_delay, 2 * base_delay)
        delay = backoff * random.random() + jitter
        return delay

    async def _get(
        self, session: httpx.AsyncClient, full_url: str, follow_redirects: bool = True, timeout: int = None
    ) -> Dict[str, Any]:
        """
        Asynchronous GET method to retrieve response data from a URL

        Args:
            session (httpx.AsyncClient): The HTTPX async client session.
            full_url (str): The full URL to send the GET request to.
            follow_redirects (bool, optional): Whether to follow redirects. Defaults to True.
            timeout (int, optional): The request timeout in seconds. Defaults to None.

        Returns:
            Dict[str, Any]: A dictionary containing the response status and data.
        """
        retry_count = 0
        headers = {
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.1234.567 Safari/537.36"
        }

        while True:
            async with self.semaphore:
                try:
                    response = await session.get(
                        url=full_url, follow_redirects=follow_redirects, headers=headers, timeout=timeout
                    )
                    if response.status_code == 403:
                        raise Exception(f"HTTP Client Error {response.status_code}: Forbidden")
                    elif response.status_code == 429 and retry_count < self.max_retries:
                        retry_count += 1
                        delay = Check24BaseScraper.exponential_backoff()
                        await asyncio.sleep(delay)
                    else:
                        response.raise_for_status()
                        if self.bucket_name:
                            await self.store_html_in_s3(full_url, response.text)
                        return {"status": "Success", "data": response}
                except httpx.HTTPStatusError as e:
                    msg = f"HTTP error occurred: {e}"
                    return {"status": "Failed", "status_reason": msg}
                except httpx.RequestError as e:
                    msg = f"Request error occurred: {e}"
                    return {"status": "Failed", "status_reason": msg}
                except Exception as e:
                    msg = f"Request Exception error occurred: {e}"
                    return {"status": "Failed", "status_reason": msg}

    async def store_html_in_s3(self, url: str, html_content: str):
        if not self.bucket_name:
            logger.warning("S3 bucket name not provided. Skipping S3 storage.")
            return
        if not self.html_s3_prefix:
            logger.warning("S3 html store age path not provided. Skipping S3 storage.")
            return

        # Generate a unique key for S3
        crawler_run_date = datetime.today().strftime("%Y-%m-%d")
        url_hash = hashlib.md5(url.encode()).hexdigest()
        s3_key = f"{self.html_s3_prefix}/{crawler_run_date}/{url_hash}.html.gz"

        # Compress the HTML content
        compressed_content = gzip.compress(html_content.encode("utf-8"))

        # Upload to S3
        try:
            self.s3_client.upload_fileobj(
                BytesIO(compressed_content),
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    "ContentType": "application/gzip",
                    "ACL": "bucket-owner-full-control",
                    "Metadata": {"original_url": url, "crawler_run_date": crawler_run_date},
                },
            )
            logger.info(f"Successfully stored HTML for {url} in S3")
        except Exception as e:
            logger.error(f"Error storing HTML in S3 for {url}: {str(e)}")

    @staticmethod
    def flatten_json(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Flatten a list of dictionaries containing nested dictionaries/lists into a list of flat dictionaries.

        Args:
            data (List[Dict[str, Any]]): The list of dictionaries to flatten.
        Returns:
            List[Dict[str, Any]]: The flattened list of dictionaries.
        """
        records = []
        index = None
        url = None
        for item in data:
            if isinstance(item, dict):
                if "index" in item:
                    index = item["index"]
                elif "url" in item:
                    url = item["url"]
            elif isinstance(item, list) and index is not None:
                for record in item:
                    record_with_index = record.copy()
                    record_with_index["index"] = index
                    record_with_index["url"] = url
                    records.append(record_with_index)
        return records

    @staticmethod
    async def get_content(response: Response) -> Dict[str, Union[str, html.HtmlElement]]:
        """
        Method to parse content containing price components from different insurance providers.

        Args:
            response (Response): The response object containing HTML content.

        Returns:
            Dict[str, Union[str, html.HtmlElement]]: A dictionary containing the status and
            either the status reason or the parsed HTML content, depending on success or failure.
        """
        try:
            # Extract the HTML content from the response
            html_string = response.text

            # Define the start and end strings for the script block to be filtered
            script_string_start = '<script type="text/template" id="remainingTariffs">'
            script_string_end = "</script>"

            # Find the indices of the start and end strings within the HTML content
            first_index = html_string.index(script_string_start)
            second_index = html_string.find(script_string_end, first_index)

            # Remove the script block from the HTML content
            html_filtered_string = (
                html_string[:first_index]
                + html_string[first_index + len(script_string_start) : second_index]
                + html_string[first_index:second_index]
                + html_string[second_index + len(script_string_end) :]
            )

            # Parse the filtered HTML content into an HTML element
            html_content = html.fromstring(html_filtered_string)

            # Return a dictionary indicating success and the parsed HTML content with pricing quote blocks
            return {"status": "Success", "html_content": html_content}
        except Exception as e:
            # If an error occurs during parsing, return a dictionary indicating failure
            message = f"Error parsing html content: {e}"
            return {"status": "Failed", "status_reason": message}

    def df_transformation(self, chunk: pd.DataFrame) -> pd.DataFrame:
        """Transform the input DataFrame."""
        raise NotImplementedError

    def create_url(self, args: Any, data_dict: Dict = None) -> str:
        """Create the URL for scraping."""
        raise NotImplementedError

    async def fetch_web_data(self, session: httpx.AsyncClient, full_url: str) -> List[Dict[str, str]]:
        """Fetch web data from the specified URL."""
        raise NotImplementedError

    async def process_parameters(self, session: httpx.AsyncClient, args: Any) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Process input parameters from CSV data chunks and yield a DataFrame with scraped dataset.

        Args:
            session (AsyncClient): The asynchronous HTTP client session.
            args (Any): Arguments containing CSV Path and other parameters.

        Yields:
            pd.DataFrame: Processed data as a DataFrame.
        """
        try:
            # Read CSV data in chunks
            with open_maybe_zipped(str(args.csv), "r") as file:
                chunks = pd.read_csv(file, chunksize=args.chunk_size)

                for i, chunk in enumerate(chunks):
                    start_time = time.perf_counter()
                    # Parse CSV data into dictionaries
                    self.list_of_dictionaries = self.df_transformation(chunk).to_dict(orient="records")
                    output_array = []
                    tasks = []
                    for data_dict in self.list_of_dictionaries:
                        url = self.create_url(args, data_dict)
                        tasks.append(self.fetch_web_data(session, url))

                    results = await asyncio.gather(*tasks)
                    for data_dict, result in zip(self.list_of_dictionaries, results):
                        output_array.extend(
                            (
                                {"index": data_dict.get("index")},
                                {"url": url},
                                result,
                            )
                        )

                    flattened_data = Check24BaseScraper.flatten_json(output_array)
                    data_df = pd.DataFrame(flattened_data)
                    # Drop unnecessary columns
                    columns_to_drop = ["currency", "features"]
                    # Check if columns exist in the DataFrame before dropping them
                    columns_to_drop = [col for col in columns_to_drop if col in data_df.columns]
                    if columns_to_drop:
                        data_df = data_df.drop(columns=columns_to_drop)

                    # Add metadata columns
                    data_df["crawler_run_date"] = datetime.today().strftime("%Y-%m-%d")
                    data_df["experiment_filename"] = str(args.csv).split("/")[-1]
                    data_df["experiment_file_md5_hash"] = args.md5_hash
                    data_df["experiment_name"] = "StandardBasketIndexchange"
                    data_df["project_name"] = "StandardBasket"

                    # Combine provider and tariffName into a single column
                    data_df["tariff"] = data_df["provider"] + ":" + data_df["tariff_name"]

                    # Convert 'price' column to numeric
                    data_df["price"] = pd.to_numeric(data_df["price"], errors="coerce")

                    logger.info(f"Total time to process each chuck: {time.perf_counter() - start_time} seconds")

                    # Yield processed DataFrame
                    yield data_df

        except Exception as e:
            logger.error(f"Error processing the requests: {e}")
            raise e
