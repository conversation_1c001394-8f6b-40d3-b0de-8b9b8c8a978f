import argparse
import logging
import os
import sys
import time
from datetime import datetime
from typing import Optional

import pandas as pd
from comparis_scraper.scrapers.turing_insurance_scraper import TuringInsuranceScraper
from pricing_analysts_adhoc_scripts.utils.file_utils import ensure_directories_exist

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger()
logger.setLevel(logging.INFO)


def crawl_turing_motor_insurance_simple(
    input_file_path: str,
    aggregation_file_path: str,
    output_file_path: str,
    api_key: str,
) -> Optional[pd.DataFrame]:
    """
    Crawl Turing motor insurance data in a single batch request (like legacy).

    Args:
        input_file_path: Path to input CSV file containing parameters
        aggregation_file_path: Path to aggregation xlsx file
        output_file_path: Path where output CSV will be saved
        api_key: Turing API key for authentication

    Returns:
        DataFrame with results, or None if no data processed

    Raises:
        Exception: If an error occurs during crawling or processing
    """
    execution_start_time = time.time()

    try:
        ensure_directories_exist(output_file_path)
        logger.info(f"Starting Turing motor insurance crawl")
        # Initialize scraper
        scraper = TuringInsuranceScraper(
            api_key=api_key, input_file_path=input_file_path, aggregation_file_path=aggregation_file_path
        )

        result_df = scraper.extract_turing_data()

        if result_df is not None and not result_df.empty:
            # Save results
            logger.info("Saving results...")
            result_df.to_csv(output_file_path)
            return result_df
        else:
            logger.warning("No results found during crawling")
            return None

    except Exception as e:
        logger.error(f"Error during Turing motor insurance crawl: {str(e)}")
        raise
    finally:
        # Log execution time
        execution_time_seconds = time.time() - execution_start_time
        execution_time_minutes = execution_time_seconds / 60

        logger.info(
            f"Crawl completed in {execution_time_seconds:.2f} seconds " f"({execution_time_minutes:.2f} minutes)"
        )


def parse_command_line_arguments() -> argparse.Namespace:
    """
    Parse command line arguments for the crawler.

    Returns:
        Namespace containing the parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Crawl Turing API for motor insurance pricing data (single batch)",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    # Define timestamp for default output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    parser.add_argument(
        "--input",
        type=str,
        help="Path to input CSV file with parameters",
        default="./../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv",
    )

    parser.add_argument(
        "--aggregation_file_path",
        type=str,
        help="aggregation file path",
        default="./../input_parameters/comparis/config/ObjectPerilStructureEFAG.xlsx",
    )

    parser.add_argument(
        "--output",
        type=str,
        help="Path to output CSV file for results",
        default=f"./output_data/output_data_turing_motor_simple_{timestamp}.csv",
    )

    parser.add_argument(
        "--api-key",
        type=str,
        help="Turing API key for authentication",
        default="",
    )
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_command_line_arguments()

    # Run the crawler
    try:
        turing_data = crawl_turing_motor_insurance_simple(
            input_file_path=args.input,
            aggregation_file_path=args.aggregation_file_path,
            output_file_path=args.output,
            api_key=args.api_key,
        )

        if turing_data is not None:
            logger.info("🎉 Turing motor insurance crawl completed successfully!")
            logger.info(f"Results saved to: {args.output}")
        else:
            logger.warning("⚠️  Crawl completed but no results were generated")

    except KeyboardInterrupt:
        logger.info("Crawl interrupted by user")
    except Exception as e:
        logger.error(f"Crawl failed: {str(e)}")
        exit(1)
