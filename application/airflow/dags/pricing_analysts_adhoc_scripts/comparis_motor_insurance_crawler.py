"""
Comparis Motor Insurance Crawler

This module provides functionality to crawl Comparis.ch for motor insurance pricing data.
It handles the entire process from reading input parameters to saving the final results.

The crawler uses asynchronous HTTP requests to efficiently process multiple insurance
quote requests concurrently while respecting rate limits.
"""

import argparse
import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path

from comparis_scraper.models.comparis_motor_insurance_request import ComparisMotorScraperArguments
from comparis_scraper.scrapers.comparis_motor_insurance_scraper import ComparisMotorInsuranceScraper
from comparis_scraper.utils.data_processing import save_results_to_csv
from utils.file_utils import ensure_directories_exist
from utils.http_utils import setup_session

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger()
logger.setLevel(logging.INFO)


async def crawl_comparis_motor_insurance(
    input_file_path: str,
    output_file_path: str,
    max_concurrent_requests: int = 10,
    chunk_size: int = 500,
    max_retries: int = 3,
    polling_interval_seconds: int = 10,
    polling_timeout_seconds: int = 120,
) -> None:
    """
    Crawl Comparis motor insurance data and save results to CSV.

    Args:
        input_file_path: Path to input CSV file containing parameters
        output_file_path: Path where output CSV will be saved
        max_concurrent_requests: Maximum number of concurrent requests
        chunk_size: Number of records to process in each chunk
        max_retries: Maximum number of retry attempts for failed requests
        polling_interval_seconds: Time in seconds between polling attempts
        polling_timeout_seconds: Maximum time in seconds to wait for results

    Raises:
        Exception: If an error occurs during crawling or processing
    """
    execution_start_time = time.time()
    result_dataframes = []
    total_records_processed = 0

    try:
        logger.info(f"Starting Comparis motor insurance crawl with {max_concurrent_requests} concurrent requests")
        logger.info(f"Reading input from: {input_file_path}")

        # Validate input file exists
        input_path = Path(input_file_path)
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file_path}")

        # Create output directory if it doesn't exist
        ensure_directories_exist(output_file_path)

        # Setup HTTP session and process data
        async with await setup_session() as session:
            # Initialize request rate limiter
            request_semaphore = asyncio.Semaphore(max_concurrent_requests)

            # Initialize scraper with optimized parameters
            scraper = ComparisMotorInsuranceScraper(
                session=session,
                semaphore=request_semaphore,
                max_retries=max_retries,
                polling_interval_seconds=polling_interval_seconds,
                polling_timeout_seconds=polling_timeout_seconds,
            )

            # Create arguments for the scraper
            scraper_args = ComparisMotorScraperArguments(csv=input_path, chunk_size=chunk_size)

            # Process data in chunks
            chunk_count = 0
            async for result_df in scraper.crawl_and_process_data(scraper_args):
                chunk_count += 1
                records_in_chunk = len(result_df)
                total_records_processed += records_in_chunk

                result_dataframes.append(result_df)

                # Log progress
                logger.info(
                    f"Processed chunk {chunk_count} with {records_in_chunk} records "
                    f"(total: {total_records_processed})"
                )

        if result_dataframes:
            # Combine results and save to CSV
            save_results_to_csv(result_dataframes, output_file_path)
        else:
            logger.warning("No results found during crawling")
            return None

    except Exception as e:
        logger.error(f"Error during Comparis motor insurance crawl: {str(e)}")
        raise
    finally:
        # Log execution time
        execution_time_seconds = time.time() - execution_start_time
        execution_time_minutes = execution_time_seconds / 60

        logger.info(
            f"Crawl completed in {execution_time_seconds:.2f} seconds " f"({execution_time_minutes:.2f} minutes)"
        )

        if total_records_processed > 0:
            logger.info(
                f"Average processing time: {execution_time_seconds / total_records_processed:.2f} "
                f"seconds per record"
            )


def parse_command_line_arguments() -> argparse.Namespace:
    """
    Parse command line arguments for the crawler.

    Returns:
        Namespace containing the parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Crawl Comparis.ch for motor insurance pricing data",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    # Define timestamp for default output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    parser.add_argument(
        "--input",
        type=str,
        help="Path to input CSV file with parameters",
        default="./../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv",
    )

    parser.add_argument(
        "--output",
        type=str,
        help="Path to output CSV file for results",
        default=f"./output_data/output_data_comparis_motor_{timestamp}.csv",
    )

    parser.add_argument("--max-concurrent", type=int, default=10, help="Maximum number of concurrent requests")

    parser.add_argument("--chunk-size", type=int, default=100, help="Number of records to process in each chunk")

    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_command_line_arguments()

    # Run the crawler
    asyncio.run(
        crawl_comparis_motor_insurance(
            input_file_path=args.input,
            output_file_path=args.output,
            max_concurrent_requests=args.max_concurrent,
            chunk_size=args.chunk_size,
        )
    )
