crawler_id,status,error,results,input_request,retry_attempts,crawler_run_date,experiment_filename,experiment_name,experiment_file_md5_hash
0,success,,"{""ResultList"": [{""ResultItemDetail"": {""LiabilityCoverPremium"": 585.8, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1272.5, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 101.5}, ""ProductId"": 10, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_Clever_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1959.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""clever"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""10"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 654.0, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1239.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 103.8}, ""ProductId"": 56, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Small_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 4.9, ""ComparisAward"": 0, ""IsWinner"": false, ""OfferQuote"": 1996.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""fair"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""56"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 635.8, ""LiabilityCoverDeductibleYoungDriver"": null, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1265.26, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 104.55}, ""ProductId"": 1085, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEX"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2005.61, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""1085"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 724.5, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1241.5, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 107.9}, ""ProductId"": 29, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 4.9, ""ComparisAward"": 0, ""IsWinner"": false, ""OfferQuote"": 2073.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""nice"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""29"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 769.9, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1304.9, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 113.7}, ""ProductId"": 57, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Budget_Tabtop"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2188.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""smart"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""57"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 851.2, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1304.9, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 118.4}, ""ProductId"": 73, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Autoversicherung"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2274.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 6, ""ProductId"": ""73"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 813.5, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 566.0, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 621.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 123.3}, ""ProductId"": 68, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Optima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2293.0, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""optima"", ""ProductType"": ""Standard"", ""ProductPosition"": 7, ""ProductId"": ""68"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1048.3, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 224.6, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 939.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 122.4}, ""ProductId"": 1076, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichOptima_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2350.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""optimum"", ""ProductType"": ""Standard"", ""ProductPosition"": 8, ""ProductId"": ""1076"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 748.0, ""LiabilityCoverDeductibleYoungDriver"": null, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1488.6, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 122.25}, ""ProductId"": 1086, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXPlus"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2358.85, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex plus"", ""ProductType"": ""Standard"", ""ProductPosition"": 9, ""ProductId"": ""1086"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1084.7, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 224.6, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 939.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 124.4}, ""ProductId"": 59, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichFlex_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2388.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 10, ""ProductId"": ""59"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 813.5, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 658.2, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 722.2, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 129.4}, ""ProductId"": 67, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Classic_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2507.6, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""classic"", ""ProductType"": ""Standard"", ""ProductPosition"": 11, ""ProductId"": ""67"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 635.8, ""LiabilityCoverDeductibleYoungDriver"": null, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 802.41, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 80.99}, ""ProductId"": 1087, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXMinima"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1519.2, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""1087"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 690.4, ""LiabilityCoverDeductibleYoungDriver"": 500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 186.8, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 591.6, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 91.2}, ""ProductId"": 66, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Minima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1717.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""66"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 800.5, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 224.6, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 869.4, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 105.1}, ""ProductId"": 58, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichBasic_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1999.6, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""basic"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""58"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}], ""IsFinished"": true}","{""Header"": {""Language"": ""en""}, ""UserEmail"": """", ""InsuranceStartDate"": ""2025-11-09T11:46:23.000Z"", ""CarInput"": {""ImmatriculationMonth"": 8, ""ImmatriculationYear"": 2025, ""SearchMode"": 3, ""CarMake"": 171272, ""CarModelSeries"": 172336, ""CarModel"": 205134, ""CarGearType"": 180007, ""CarType"": 102228861, ""CarEquipmentPrice"": 2000, ""YearOfPurchase"": 2024, ""IsLeased"": true, ""KmPerYear"": 10000, ""CarUsage"": 1, ""CarGarage"": 1, ""CarRegistrationCanton"": ""BL"", ""CarTypeCertificate"": ""1TB530"", ""LicenseCanton"": """", ""LicenseNumber"": """"}, ""Driver"": {""BirthDate"": ""2006-08-09T00:00:00.000Z"", ""Gender"": 2, ""TownId"": ""17643"", ""Nationality"": ""FR"", ""ResidencePermit"": 3, ""IsDriverInsuranceTaker"": true, ""LicenseDate"": ""2024-08-09T00:00:00.000Z"", ""IsOtherDriverUnder25"": false, ""CurrentProvider"": 99999}, ""Coverages"": {""CoverageType"": 3, ""RetentionPartialCover"": 500, ""RetentionCollisionCascoCover"": 2000, ""HasBonusProtection"": true, ""HasParkingDamage"": true, ""HasParkingDamageUnlimited"": true, ""HasPersonalEffects"": true, ""HasPassengerAccident"": false, ""WantsPreselection"": true, ""HasGrossNegligence"": true, ""HasAssistance"": false}, ""ClaimsAndConvictionsQuestions"": {""HasLiabilityDamage"": false, ""HasCascoAndParkingDamage"": false, ""HasDrivingLicenseSuspension"": false, ""HasImprisonmentRecords"": false, ""HasRequestRejected"": false, ""HasTerminatedByInsurance"": false}, ""UseNeolutionPush"": false}",1,2025-08-11,ComparisEFAG_test.csv,August25,
1,success,,"{""ResultList"": [{""ResultItemDetail"": {""LiabilityCoverPremium"": 297.5, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1631.6, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 102.8}, ""ProductId"": 57, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Budget_Tabtop"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2031.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""smart"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""57"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 328.9, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1631.6, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 104.6}, ""ProductId"": 73, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Autoversicherung"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2065.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""73"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 249.3, ""LiabilityCoverDeductibleYoungDriver"": 3000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1773.92, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 107.52}, ""ProductId"": 1085, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEX"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2130.74, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""1085"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 253.2, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1842.32, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 111.16}, ""ProductId"": 1086, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXPlus"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2206.68, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex plus"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""1086"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 318.2, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1908.8, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 118.0}, ""ProductId"": 56, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Small_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 4.9, ""ComparisAward"": 0, ""IsWinner"": false, ""OfferQuote"": 2345.0, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""fair"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""56"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 351.7, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1908.8, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 119.9}, ""ProductId"": 29, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 4.9, ""ComparisAward"": 0, ""IsWinner"": false, ""OfferQuote"": 2380.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""nice"", ""ProductType"": ""Standard"", ""ProductPosition"": 6, ""ProductId"": ""29"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 647.5, ""LiabilityCoverDeductibleYoungDriver"": 2500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 358.8, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1226.9, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 120.2}, ""ProductId"": 1076, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichOptima_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2380.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""optimum"", ""ProductType"": ""Standard"", ""ProductPosition"": 7, ""ProductId"": ""1076"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 663.2, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 358.8, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1226.9, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 121.2}, ""ProductId"": 59, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichFlex_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2397.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 8, ""ProductId"": ""59"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 970.3, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1306.7, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 122.0}, ""ProductId"": 44, ""ProviderId"": 10, ""ProviderNameInfobase"": ""AV8_DB_Provider_Helvetia"", ""ProductNameInfobase"": ""AV8_Productname_Helvetia_Premium_Tabtop"", ""LogoName"": ""Helvetia_r6pgkq"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2414.0, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_Helvetia_Taptop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""helvetia"", ""ProductName"": ""autoversicherung plus"", ""ProductType"": ""Standard"", ""ProductPosition"": 9, ""ProductId"": ""44"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 592.1, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 1135.3, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 863.0, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 150.5}, ""ProductId"": 68, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Optima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2924.2, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""optima"", ""ProductType"": ""Standard"", ""ProductPosition"": 10, ""ProductId"": ""68"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 539.1, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 1320.1, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1003.5, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 162.0}, ""ProductId"": 67, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Classic_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 3229.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""classic"", ""ProductType"": ""Standard"", ""ProductPosition"": 11, ""ProductId"": ""67"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 412.0, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 3084.3, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 182.1}, ""ProductId"": 10, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_Clever_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 3678.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""clever"", ""ProductType"": ""Standard"", ""ProductPosition"": 12, ""ProductId"": ""10"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 453.6, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 3254.4, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 193.0}, ""ProductId"": 31, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_premium_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 3901.0, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 13, ""ProductId"": ""31"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 249.3, ""LiabilityCoverDeductibleYoungDriver"": 3000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1054.89, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 71.29}, ""ProductId"": 1087, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXMinima"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1375.48, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""1087"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 387.1, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1059.6, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 79.4}, ""ProductId"": 45, ""ProviderId"": 10, ""ProviderNameInfobase"": ""AV8_DB_Provider_Helvetia"", ""ProductNameInfobase"": ""AV8_Productname_Helvetia_Budget_Tabtop"", ""LogoName"": ""Helvetia_r6pgkq"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1526.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_Helvetia_Taptop""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""helvetia"", ""ProductName"": ""autoversicherung economy"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""45"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 489.6, ""LiabilityCoverDeductibleYoungDriver"": 500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 288.4, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 621.4, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 85.2}, ""ProductId"": 66, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Minima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1626.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""66"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 374.1, ""LiabilityCoverDeductibleYoungDriver"": 3000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 358.8, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1079.7, ""ComprehensiveCoverDeductibleYoungDriver"": 4000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 97.8}, ""ProductId"": 58, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichBasic_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1910.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""basic"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""58"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 412.0, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 2110.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": true, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 133.4}, ""ProductId"": 30, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_budget_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 2655.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 3, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""budget"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""30"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}], ""IsFinished"": true}","{""Header"": {""Language"": ""en""}, ""UserEmail"": """", ""InsuranceStartDate"": ""2025-11-09T11:46:30.000Z"", ""CarInput"": {""ImmatriculationMonth"": 8, ""ImmatriculationYear"": 2025, ""SearchMode"": 3, ""CarMake"": 118931, ""CarModelSeries"": 125027, ""CarModel"": 215073, ""CarGearType"": 180010, ""CarType"": 102231505, ""CarEquipmentPrice"": 2000, ""YearOfPurchase"": 2024, ""IsLeased"": true, ""KmPerYear"": 10000, ""CarUsage"": 4, ""CarGarage"": 1, ""CarRegistrationCanton"": ""FR"", ""CarTypeCertificate"": ""ABA378"", ""LicenseCanton"": """", ""LicenseNumber"": """"}, ""Driver"": {""BirthDate"": ""1963-08-09T00:00:00.000Z"", ""Gender"": 1, ""TownId"": ""17196"", ""Nationality"": ""CH"", ""ResidencePermit"": null, ""IsDriverInsuranceTaker"": true, ""LicenseDate"": ""2013-08-09T00:00:00.000Z"", ""IsOtherDriverUnder25"": false, ""CurrentProvider"": 99999}, ""Coverages"": {""CoverageType"": 3, ""RetentionPartialCover"": 1000, ""RetentionCollisionCascoCover"": 1000, ""HasBonusProtection"": true, ""HasParkingDamage"": true, ""HasParkingDamageUnlimited"": true, ""HasPersonalEffects"": true, ""HasPassengerAccident"": false, ""WantsPreselection"": true, ""HasGrossNegligence"": false, ""HasAssistance"": false}, ""ClaimsAndConvictionsQuestions"": {""HasLiabilityDamage"": false, ""HasCascoAndParkingDamage"": false, ""HasDrivingLicenseSuspension"": false, ""HasImprisonmentRecords"": false, ""HasRequestRejected"": false, ""HasTerminatedByInsurance"": false}, ""UseNeolutionPush"": false}",1,2025-08-11,ComparisEFAG_test.csv,August25,
