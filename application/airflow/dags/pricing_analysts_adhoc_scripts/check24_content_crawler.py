import argparse
import asyncio
import logging
from pathlib import Path

import httpx
import pandas as pd
from check24_crawlers.check24_content_scrapper import (
    Check24ContentInsuranceScraper,
    Check24ContentInsuranceScraperArguments,
)
from utils.file_utils import ensure_directories_exist

# Set display options for pandas
pd.set_option("display.max_rows", None)
pd.set_option("display.max_columns", None)

# Configure logging
logging.basicConfig(level=logging.WARNING)


async def main(input_file: str, output_file: str):
    # Define the path to the CSV file containing input parameters
    csv_path = Path(input_file)

    args_instance = Check24ContentInsuranceScraperArguments(csv=csv_path, chunk_size=30)

    # Set up a semaphore to limit concurrent requests
    semaphore: asyncio.Semaphore = asyncio.Semaphore(10)

    scrapper = Check24ContentInsuranceScraper(semaphore=semaphore, max_retries=10)

    # Define the path to the output CSV file
    ensure_directories_exist(output_file)
    output_csv_file = Path(output_file)

    # Check if the output file exists
    if output_csv_file.exists():
        # Delete the file if it exists
        output_csv_file.unlink()

    async with httpx.AsyncClient() as session:
        # Iterate over the processed data frames from the scrapper
        async for data_df in scrapper.process_parameters(session, args_instance):
            # Write the data frame to the output CSV file
            data_df.to_csv(output_csv_file, mode="a", index=False, header=not output_csv_file.exists())


if __name__ == "__main__":
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Fetch web data and save to CSV")
    parser.add_argument(
        "--input-file",
        type=str,
        default="./../input_parameters/dataforcontentcrawler.csv",
        help="Path to the input CSV file containing parameters",
    )
    parser.add_argument(
        "--output-file", type=str, default="./output_data/sample_data_content.csv", help="Path to the output CSV file"
    )
    args = parser.parse_args()

    # Run the main asynchronous function with command-line arguments
    asyncio.run(main(input_file=args.input_file, output_file=args.output_file))
