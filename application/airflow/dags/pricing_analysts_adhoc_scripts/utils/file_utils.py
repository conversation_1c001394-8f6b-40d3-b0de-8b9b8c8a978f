"""
File system utilities.

This module provides functions for common file system operations
including directory creation and path manipulation.
"""
import logging
import os
from pathlib import Path
from typing import Union

logger = logging.getLogger(__name__)


def ensure_directories_exist(file_path: Union[str, Path]) -> None:
    """
    Ensure that all directories in the given file path exist.

    Args:
        file_path: Path to file for which directories should be created

    Example:
        >>> ensure_directories_exist('/path/to/new/directory/file.txt')
        # Creates all parent directories if they don't exist
    """
    directory = os.path.dirname(str(file_path))
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Created directory: {directory}")
    else:
        logger.debug(f"Directory already exists: {directory}")
