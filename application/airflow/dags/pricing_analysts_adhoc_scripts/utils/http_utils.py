"""
HTTP client utility functions.

This module provides functions for setting up HTTP clients with proxy support
and SSL certificate handling for web scraping operations.
"""
import logging
import tempfile
from typing import Dict, Optional

import httpx
from pricing_analysts_adhoc_scripts.utils.aws_utils import fetch_ssm_parameters

logger = logging.getLogger(__name__)


async def setup_session(use_proxy: bool = True, timeout: int = 30, follow_redirects: bool = True) -> httpx.AsyncClient:
    """
    Setup HTTP session with proxy and certificate from SSM.

    Args:
        use_proxy: Whether to use proxy configuration
        timeout: Request timeout in seconds
        follow_redirects: Whether to follow HTTP redirects

    Returns:
        Configured HTTPX AsyncClient

    Raises:
        Exception: If proxy configuration fails

    Example:
        >>> async with setup_session() as session:
        >>>     response = await session.get("https://example.com")
    """
    proxy_config: Dict[str, str] = {}
    cert_path: Optional[str] = None

    if use_proxy:
        try:
            # Get proxy configuration from SSM
            base_path = "/dev/datalake/brightdata/webscraper"
            parameters = fetch_ssm_parameters([f"{base_path}/proxy-config", f"{base_path}/proxy/ssl_certificate"])

            # Parse proxy configuration
            raw_proxy_config = parameters.get(f"{base_path}/proxy-config", {})
            if isinstance(raw_proxy_config, str):
                try:
                    raw_proxy_config = eval(raw_proxy_config)
                except Exception as e:
                    logger.error(f"Error parsing proxy configuration: {str(e)}")
                    raise

            # Format proxy URLs correctly
            proxy_config = {f"{scheme}://": url for scheme, url in raw_proxy_config.items()}

            # Save certificate to temporary file
            cert_content = parameters.get(f"{base_path}/proxy/ssl_certificate", "")
            if cert_content:
                with tempfile.NamedTemporaryFile(suffix=".crt", delete=False) as cert_file:
                    cert_file.write(cert_content.encode())
                    cert_path = cert_file.name
                    logger.debug(f"SSL certificate saved to: {cert_path}")

        except Exception as e:
            logger.error(f"Failed to get proxy from SSM: {str(e)}")
            raise

    # Create and return the HTTP client
    return httpx.AsyncClient(proxies=proxy_config, verify=cert_path, timeout=timeout, follow_redirects=follow_redirects)
