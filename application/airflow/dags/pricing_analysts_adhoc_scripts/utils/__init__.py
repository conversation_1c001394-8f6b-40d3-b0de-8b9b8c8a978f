"""
Utility Functions for Pricing Analysts Adhoc Scripts

This package provides core utility functions used specifically for pricing analysts
adhoc scripts in the price crawler application, organized by functionality:

- aws_utils: AWS service interactions (SSM, S3, etc.)
- file_utils: File system operations
- string_utils: String manipulation utilities
- http_utils: HTTP client and request handling with proxy support

Most commonly used functions are exposed at the package level for convenience.
"""

# Import commonly used functions to make them available directly
from .aws_utils import fetch_ssm_parameters
from .file_utils import ensure_directories_exist
from .http_utils import setup_session

# Define what's available when using "from pricing_analysts_adhoc_scripts.utils import *"
__all__ = [
    # AWS utilities
    "fetch_ssm_parameters",
    # File utilities
    "ensure_directories_exist",
    # HTTP utilities
    "setup_session",
]
