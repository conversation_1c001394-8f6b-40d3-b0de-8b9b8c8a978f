"""
AWS service utility functions.

This module provides functions for interacting with AWS services like SSM Parameter Store,
S3, and other AWS resources used across the price crawler application.
"""
import logging
from typing import Dict, List

import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


def fetch_ssm_parameters(parameter_names: List[str]) -> Dict[str, str]:
    """
    Retrieve parameters from AWS SSM Parameter Store.

    Args:
        parameter_names: List of parameter names to retrieve

    Returns:
        Dictionary mapping parameter names to their values

    Raises:
        ClientError: If there's an issue with the AWS API call

    Example:
        >>> params = fetch_ssm_parameters(['/app/dev/database/url', '/app/dev/api/key'])
        >>> db_url = params.get('/app/dev/database/url')
    """
    try:
        ssm_client = boto3.client("ssm")
        response = ssm_client.get_parameters(Names=parameter_names, WithDecryption=True)

        # Check for invalid parameters
        invalid_parameters = response.get("InvalidParameters", [])
        if invalid_parameters:
            logger.warning(f"Invalid parameters: {', '.join(invalid_parameters)}")

        return {param["Name"]: param["Value"] for param in response["Parameters"]}
    except ClientError as e:
        logger.error(f"Error retrieving SSM parameters: {str(e)}")
        raise
