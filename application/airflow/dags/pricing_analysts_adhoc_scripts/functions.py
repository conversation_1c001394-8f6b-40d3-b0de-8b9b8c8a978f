import pandas as pd
import json
import re

# Set display options for pandas
pd.set_option("display.max_rows", None)
pd.set_option("display.max_columns", None)

df = pd.read_csv("/Users/<USER>/repos/price-crawler/application/airflow/dags/input_parameters/comparis/comparis_motor/ComparisEFAG.csv")
df_rm = pd.read_csv("/Users/<USER>/Downloads/d88b5840-7afd-42af-b40f-c75ab25aa882.csv")

print(df.head(2))

filtered_df = df[df['CrawlerID'].isin(df_rm['crawler_id'])]
filtered_df.to_csv('/Users/<USER>/repos/price-crawler/application/airflow/dags/input_parameters/comparis/comparis_motor/filtered_rows.csv', index=False)