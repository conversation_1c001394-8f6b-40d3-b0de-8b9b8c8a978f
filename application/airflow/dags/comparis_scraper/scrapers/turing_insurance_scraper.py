import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import pandas as pd
import requests
from comparis_scraper.constants.turing_mappings import DEFAULT_PRICING_VERSION, DISPLACEMENT_COLUMN_MAPPING
from comparis_scraper.constants.urls import TURING_BASE_URL, XCALLER_INTERNAL
from comparis_scraper.transformers.turing_transformer import TuringTransformer

logger = logging.getLogger(__name__)


class TuringInsuranceScraper:
    """
    Synchronous scraper for Turing insurance quotes.

    This class handles the process of fetching insurance quotes from the Turing API,
    including data transformation, request submission, and result processing.
    """

    def __init__(
        self,
        base_url: str = TURING_BASE_URL,
        api_key: str = None,
        max_retries: int = 3,
        input_file_path: str = None,
        aggregation_file_path: str = None,
        experiment_file_md5_hash: str = None,
    ):
        """Initialize the TuringInsuranceScraper."""
        self.base_url = base_url
        self.api_key = api_key
        self.max_retries = max_retries
        self.input_file_path = input_file_path
        self.aggregation_file_path = aggregation_file_path
        self.experiment_file_md5_hash = experiment_file_md5_hash
        self.transformer = TuringTransformer()

    def get_headers(self) -> Dict:
        """Get request headers including API key."""
        headers = {"accept": "*/*", "Content-Type": "application/json", **XCALLER_INTERNAL}
        if self.api_key:
            headers["api-key"] = self.api_key
        return headers

    def extract_turing_data(self) -> pd.DataFrame:
        """
        extract pricing data from Turing API, for give input experiment baskets

        Returns:
            DataFrame containing turing results
        """
        try:
            logger.info(f"Reading input from: {self.input_file_path}")

            # Validate input file exists
            input_path = Path(self.input_file_path)
            if not input_path.exists():
                raise FileNotFoundError(f"Input file not found: {self.input_file_path}")

            # Load input data
            logger.info("Loading input data...")
            input_data = pd.read_csv(input_path, dtype=str)
            input_data.set_index("CrawlerID", inplace=True)

            if len(input_data) == 0:
                logger.error("No valid records found in input file")
                raise ValueError(f"No valid records found in input file")

            logger.info(f"Processing {len(input_data)} rows in batch mode")

            # Transform entire dataset to Turing format
            input_data["LEASING_DURATION"] = 0
            input_data["LEASING_END"] = 0

            # Apply displacement column renaming
            input_data = input_data.rename(columns=DISPLACEMENT_COLUMN_MAPPING)
            batch_request, indicator = self.transformer.transform_batch_data(input_data, self.aggregation_file_path)

            # Send single batch request to Turing API
            logger.info(f"Sending batch request with {len(batch_request.get('quoteRequests', []))} quote requests")
            response = requests.post(self.base_url, json=batch_request, headers=self.get_headers())
            response.raise_for_status()

            # Process batch response
            output_data = self.transformer.mapper_TuringOutputPremia(response, indicator)
            results_df = self.process_batch_response(output_data, input_data)

            logger.info(f"Batch processing completed successfully: {len(results_df)} results")
            return results_df

        except Exception as e:
            logger.error(f"Extraction form Turing api failed: {str(e)}")
            raise e

    def process_batch_response(self, output_data: pd.DataFrame, input_data: pd.DataFrame) -> pd.DataFrame:
        """
        Args:
            output_data: Post processed response from Turing API
            input_data: Original input DataFrame

        Returns:
            DataFrame with Truing data
        """
        try:
            output_final = output_data.merge(input_data, left_index=True, right_index=True, how="right")
            output_final["iptiQPriceVersion"] = DEFAULT_PRICING_VERSION
            current_date = datetime.today()
            experiment_name = f"{current_date.strftime('%B')}{str(current_date.year)[2:]}"
            experiment_filename = str(self.input_file_path).split("/")[-1]
            crawler_run_date = current_date.strftime("%Y-%m-%d")
            output_final.insert(0, "experiment_name", experiment_name)
            output_final.insert(0, "experiment_filename", experiment_filename)
            output_final.insert(0, "experiment_file_md5_hash", self.experiment_file_md5_hash)
            output_final.insert(0, "crawler_run_date", crawler_run_date)
            output_final.index.set_names("CrawlerID", inplace=True)
            output_final.reset_index()
            return output_final
        except Exception as e:
            logger.error(f"Error processing batch response: {str(e)}")
            return pd.DataFrame()
