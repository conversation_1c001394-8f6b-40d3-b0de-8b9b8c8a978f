import asyncio
import json
import logging
from datetime import datetime
from typing import Any, As<PERSON><PERSON><PERSON><PERSON>, Dict, List, <PERSON><PERSON>

import httpx
import pandas as pd
from comparis_scraper.constants.urls import COMPARIS_MOTOR_BASE_URL, MOTOR_RESULTS_PATH, MOTOR_SAVE_INPUT_PATH
from comparis_scraper.core.base_scraper import ComparisB<PERSON><PERSON>craper
from comparis_scraper.transformers.comparis_motor_insurance_transformer import ComparisMotorInsuranceTransformer

from airflow.utils.file import open_maybe_zipped

logger = logging.getLogger(__name__)


class ComparisMotorInsuranceScraper(ComparisBaseScraper):
    """
    Scraper for Comparis motor insurance quotes.

    This class handles the process of fetching motor insurance quotes from Comparis.ch,
    including data transformation, request submission, and result polling.

    Attributes:
        BASE_URL: Base URL for Comparis motor insurance API
        polling_interval_seconds: Time in seconds between polling attempts
        polling_timeout_seconds: Maximum time in seconds to wait for results
        transformer: Transformer for converting input data to Comparis format
    """

    BASE_URL = COMPARIS_MOTOR_BASE_URL

    def __init__(
        self,
        session: httpx.AsyncClient,
        semaphore: asyncio.Semaphore,
        base_url: str = COMPARIS_MOTOR_BASE_URL,
        max_retries: int = 10,
        polling_interval_seconds: int = 60,
        polling_timeout_seconds: int = 90,
    ):
        """
        Initialize the ComparisMotorInsuranceScraper.

        Args:
            session: HTTPX AsyncClient for making HTTP requests
            semaphore: Semaphore for controlling concurrent request limits
            max_retries: Maximum number of retry attempts for failed requests
            polling_interval_seconds: Time in seconds between polling attempts
            polling_timeout_seconds: Maximum time in seconds to wait for results
        """
        super().__init__(session=session, semaphore=semaphore, max_retries=max_retries)
        self.base_url = base_url
        self.polling_interval_seconds = polling_interval_seconds
        self.polling_timeout_seconds = polling_timeout_seconds
        self.transformer = ComparisMotorInsuranceTransformer()

    async def process_batch(self, batch_data: List[Dict]) -> Tuple[Dict[str, Any], ...]:
        """
        Process a batch of insurance quote requests concurrently.

        Args:
            batch_data: List of request data dictionaries to process

        Returns:
            Tuple of processed results for each request
        """
        tasks = [self.process_single_request(request) for request in batch_data]
        return await asyncio.gather(*tasks)

    async def crawl_and_process_data(self, args: Any) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Process input parameters in batches and yield result DataFrames.

        This method reads input data in chunks, transforms it, processes requests
        concurrently, and yields DataFrames with the results.

        Args:
            args: Arguments containing CSV path and chunk size

        Yields:
            DataFrame containing processed results for each chunk

        Raises:
            Exception: If an error occurs during processing
        """
        try:
            # Read CSV data in chunks
            with open_maybe_zipped(str(args.csv), "r") as file:
                chunks = pd.read_csv(file, chunksize=args.chunk_size)
                current_date = datetime.today()
                experiment_name = f"{current_date.strftime('%B')}{str(current_date.year)[2:]}"
                experiment_filename = str(args.csv).split("/")[-1]
                crawler_run_date = current_date.strftime("%Y-%m-%d")

                for chunk_index, chunk_data in enumerate(chunks):
                    logger.info(f"Processing chunk {chunk_index+1} with {len(chunk_data)} records")

                    # Transform chunk data
                    transformed_data = self.transformer.transform_and_validate(chunk_data)
                    request_data_list = transformed_data.to_dict(orient="records")

                    # Process all records in the chunk concurrently
                    tasks = [self.process_single_request(request_data) for request_data in request_data_list]
                    results = await asyncio.gather(*tasks, return_exceptions=True)

                    # Filter out exceptions and create DataFrame
                    valid_results = [result for result in results if not isinstance(result, Exception)]
                    if valid_results:
                        results_df = pd.DataFrame(valid_results)
                        results_df["crawler_run_date"] = crawler_run_date
                        results_df["experiment_filename"] = experiment_filename
                        results_df["experiment_name"] = experiment_name
                        results_df["experiment_file_md5_hash"] = args.md5_hash
                        yield results_df

                    # Log any exceptions that occurred
                    self._log_processing_exceptions(results, chunk_index)

        except Exception as e:
            logger.error(f"Error processing data: {str(e)}")
            raise

    def _log_processing_exceptions(self, results: Tuple[Any], chunk_index: int) -> None:
        """
        Log exceptions that occurred during processing.

        Args:
            results: List of results that may contain exceptions
            chunk_index: Index of the current chunk for logging context
        """
        exceptions = [result for result in results if isinstance(result, Exception)]
        if exceptions:
            logger.warning(f"Encountered {len(exceptions)} errors during processing chunk {chunk_index+1}")
            for exception_index, exception in enumerate(exceptions[:5]):  # Log first 5 exceptions
                logger.warning(f"Error {exception_index+1}: {str(exception)}")

    async def process_single_request(self, request_data: Dict) -> Dict[str, Any]:
        """
        Process a single insurance quote request.

        This method transforms the input data, submits it to Comparis,
        and polls for results. Implements retry logic for transient failures.

        Args:
            request_data: Dictionary containing request data

        Returns:
            Dictionary with request results or error information

        Raises:
            Exception: If an error occurs during processing after all retries
        """
        crawler_id = request_data.get("CrawlerID", "unknown")
        quote_request = None

        for attempt in range(1, self.max_retries + 1):
            try:
                logger.debug(f"Processing request for crawler_id: {crawler_id} (Attempt {attempt}/{self.max_retries})")

                # Transform input data to Comparis format
                quote_request = self.transformer.transform_input_data(request_data)

                # Save input and get GUID
                input_guid = await self._save_input_data(quote_request)

                # Poll for results
                quote_results = await self._poll_for_quote_results(input_guid)

                # check if ResultList is empty
                if len(quote_results.get("ResultList", [])) == 0:
                    raise ValueError("ResultList is empty")

                return {
                    "crawler_id": crawler_id,
                    "status": "success",
                    "error": None,
                    "results": json.dumps(quote_results),
                    "input_request": json.dumps(quote_request),  # Store the input request
                    "retry_attempts": attempt,
                }

            except Exception as e:
                logger.warning(
                    f"Error processing request {crawler_id} (Attempt {attempt}/{self.max_retries}): {str(e)}"
                )

                if attempt < self.max_retries:
                    # Exponential backoff with jitter
                    backoff_time = self.exponential_backoff(attempt)
                    await asyncio.sleep(backoff_time)
                    continue
                else:
                    # All retries exhausted
                    logger.error(f"Failed to process request {crawler_id} after {self.max_retries} attempts: {str(e)}")
                    return {
                        "crawler_id": crawler_id,
                        "status": "failed",
                        "error": str(e),
                        "results": None,
                        "input_request": quote_request,
                        "retry_attempts": attempt,
                    }

    async def _save_input_data(self, quote_request: Dict) -> str:
        """
        Save input data to Comparis and get response GUID.

        Args:
            quote_request: Transformed quote request data

        Returns:
            GUID string for the saved input

        Raises:
            Exception: If saving input fails
        """
        response = await self.post(url=f"{self.base_url}{MOTOR_SAVE_INPUT_PATH}", data=quote_request)

        if response.get("Header", {}).get("StatusCode") != 0:
            raise Exception(f"Failed to save input: {response}")

        return response["InputGuid"]

    async def _poll_for_quote_results(self, input_guid: str) -> Dict:
        """
        Poll for quote results with adaptive polling interval.

        This method implements an adaptive polling strategy that starts with
        shorter intervals and gradually increases them to reduce unnecessary
        requests while maintaining responsiveness.

        Args:
            input_guid: GUID for the saved input

        Returns:
            Dictionary containing quote results

        Raises:
            TimeoutError: If polling exceeds the timeout period
        """
        # Start with a shorter polling interval and increase it over time
        current_interval_seconds = max(5, self.polling_interval_seconds // 3)
        total_wait_time_seconds = 0

        while total_wait_time_seconds < self.polling_timeout_seconds:
            results_payload = [{"Header": {"Language": "en"}, "Guid": input_guid, "ShowInfoBaseKeys": False}]

            response = await self.get(
                url=f"{self.base_url}{MOTOR_RESULTS_PATH}?requestObject={json.dumps(results_payload)[1:-1]}"
            )

            if response["Result"]["IsFinished"]:
                return response["Result"]

            # Implement adaptive polling with exponential backoff
            current_interval_seconds = min(current_interval_seconds * 1.5, self.polling_interval_seconds * 2)

            logger.debug(f"Waiting {current_interval_seconds:.1f}s for results (GUID: {input_guid})")
            await asyncio.sleep(current_interval_seconds)
            total_wait_time_seconds += current_interval_seconds

        raise TimeoutError(
            f"Polling timeout exceeded after {total_wait_time_seconds:.1f} seconds for GUID: {input_guid}"
        )
