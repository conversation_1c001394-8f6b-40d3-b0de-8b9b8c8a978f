"""
DataFrame manipulation utilities.

This module provides functions for common DataFrame operations including
type conversions, value mappings, and conditional calculations.
"""

import logging
from typing import Any, Dict, List, Union

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


def convert_columns_to_numeric(df: pd.DataFrame, columns: List[str], data_type: str = "int") -> pd.DataFrame:
    """
    Convert specified DataFrame columns to a numeric type (strict mode).

    Args:
        df: Input DataFrame
        columns: List of column names to convert
        data_type: Target data type (e.g. 'int', 'float', 'Int64')

    Returns:
        DataFrame with specified columns converted to given data type.

    Raises:
        KeyError: If any requested columns are missing.
        ValueError: If conversion fails for any column.
    """
    df_copy = df.copy()

    # 🔹 Strict check: All columns must exist
    missing_cols = set(columns) - set(df_copy.columns)
    if missing_cols:
        error_msg = f"The following columns are missing in DataFrame: {missing_cols}"
        logger.error(error_msg)
        raise KeyError(error_msg)

    try:
        for col in columns:
            df_copy[col] = (
                pd.to_numeric(df_copy[col], errors="coerce")  # Convert non-numeric to NaN
                .fillna(0)  # Fill missing values with 0
                .astype(data_type)  # Convert to requested type
            )
        return df_copy
    except (ValueError, TypeError) as e:
        logger.error(f"Failed to convert columns {columns} to {data_type}: {str(e)}")
        raise


def apply_value_mappings(df: pd.DataFrame, column_mapping_dict: Dict[str, Dict[Any, Any]]) -> pd.DataFrame:
    """
    Apply value mappings to DataFrame columns.

    Args:
        df: Input DataFrame
        column_mapping_dict: Dictionary mapping column names to value mappings

    Returns:
        DataFrame with mapped values

    Example:
        >>> mappings = {'status': {'A': 'Active', 'I': 'Inactive'}}
        >>> apply_value_mappings(df, mappings)
    """
    df_copy = df.copy()
    df_copy.replace(column_mapping_dict, inplace=True)
    return df_copy


def calculate_conditional_values(conditions: List[np.ndarray], values: List[Any], default: Any = None) -> np.ndarray:
    """
    Calculate values based on conditions using numpy.select.

    Args:
        conditions: List of boolean arrays for conditions
        values: List of values corresponding to conditions
        default: Default value if no condition is met

    Returns:
        Array of calculated values based on conditions

    Example:
        >>> conditions = [df['age'] < 18, df['age'] < 65]
        >>> values = ['minor', 'adult']
        >>> df['category'] = calculate_conditional_values(conditions, values, 'senior')
    """
    return np.select(conditions, values, default)


def map_boolean_columns(df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
    """Map boolean columns from Yes/No to True/False."""
    df_copy = df.copy()
    try:
        df_copy[columns] = df_copy[columns].replace({"Yes": True, "No": False})
        return df_copy
    except (ValueError, TypeError) as e:
        logger.error(f"Failed to convert columns {columns} to integers: {str(e)}")
        raise
