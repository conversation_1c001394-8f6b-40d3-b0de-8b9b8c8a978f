"""
Model utilities for data transformations.

This module provides helper functions for working with Pydantic models for the Comparis scraper.
"""

import logging
from typing import Any, Dict, Type, TypeVar

from pydantic import BaseModel

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


def create_model_instance(data: Dict[str, Any], model_class: Type[T]) -> T:
    """
    Create and validate a Pydantic model instance from dictionary data.

    Args:
        data: Dictionary containing model data
        model_class: Pydantic model class to instantiate

    Returns:
        Validated model instance

    Example:
        >>> from pydantic import BaseModel
        >>> class User(BaseModel):
        ...     name: str
        ...     age: int
        >>> user_data = {"name": "<PERSON>", "age": 30}
        >>> user = create_model_instance(user_data, User)
        >>> user.name
        'John'
    """
    return model_class(**data)
