"""
String manipulation utilities.

This module provides functions for common string operations and transformations
used across the price crawler application.
"""

import re
from typing import Optional


def to_snake_case(name: Optional[str]) -> Optional[str]:
    """
    Convert CamelCase or PascalCase to snake_case.

    Args:
        name: String to convert to snake_case

    Returns:
        Converted string in snake_case format, or None if input is None

    Example:
        >>> to_snake_case("HelloWorld")
        'hello_world'
        >>> to_snake_case("APIResponse")
        'api_response'
    """
    # Handle empty strings or None
    if not name:
        return name
    # Insert underscore before uppercase letters and convert to lowercase
    name_with_first_replacements = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", name_with_first_replacements).lower()
