"""
Validation utilities for data transformations.

This module provides reusable validation functions for data processing
and transformation operations in the Comparis scraper.
"""

import logging
from typing import Any, Dict, List, Optional, Type

import pandas as pd
from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)


def validate_required_columns(data: pd.DataFrame, required_columns: List[str]) -> bool:
    """
    Validate that all required columns are present in the DataFrame.

    Args:
        data: DataFrame to validate
        required_columns: List of required column names

    Returns:
        True if all required columns are present

    Raises:
        ValueError: If any required columns are missing

    Example:
        >>> import pandas as pd
        >>> df = pd.DataFrame({'name': ['John', '<PERSON>'], 'age': [30, 25]})
        >>> validate_required_columns(df, ['name', 'age'])
        True
        >>> validate_required_columns(df, ['name', 'email'])
        Traceback (most recent call last):
            ...
        ValueError: Missing required columns: ['email']
    """
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    return True


def validate_model_data(data: Dict[str, Any], model_class: Type[BaseModel]) -> Optional[Dict[str, Any]]:
    """
    Validate dictionary data against a Pydantic model.

    Args:
        data: Dictionary containing data to validate
        model_class: Pydantic model class to validate against

    Returns:
        Validated data dictionary if validation succeeds

    Raises:
        ValidationError: If validation fails

    Example:
        >>> from pydantic import BaseModel
        >>> class Product(BaseModel):
        ...     name: str
        ...     price: float
        >>> data = {"name": "Laptop", "price": 999.99}
        >>> result = validate_model_data(data, Product)
        >>> result
        {'name': 'Laptop', 'price': 999.99}

        >>> invalid_data = {"name": "Laptop"}  # Missing required field
        >>> validate_model_data(invalid_data, Product)
        Traceback (most recent call last):
            ...
        pydantic.error_wrappers.ValidationError: 1 validation error for Product
        price
          field required (type=value_error.missing)
    """
    try:
        validated_data = model_class(**data)
        return validated_data.dict()
    except ValidationError as e:
        logger.error(f"Validation error: {str(e)}")
        raise
