"""
Data processing utility functions.

This module provides functions for processing and analyzing data,
including success rate calculations and file operations for DataFrames.
"""

import logging
from pathlib import Path
from typing import List, Optional, Tuple

import pandas as pd

logger = logging.getLogger(__name__)


def calculate_success_rate(dataframe: pd.DataFrame) -> Tuple[float, int, int]:
    """
    Calculate success rate of crawled records.

    Args:
        dataframe: DataFrame containing crawled results with 'status' column

    Returns:
        Tuple containing:
            - success_rate: Percentage of successful records (0-100)
            - success_count: Number of successful records
            - total_count: Total number of records

    Example:
        >>> df = pd.DataFrame({'status': ['success', 'failed', 'success']})
        >>> rate, successes, total = calculate_success_rate(df)
        >>> print(f"{rate}% ({successes}/{total})")
        '66.67% (2/3)'
    """
    success_count = len(dataframe[dataframe["status"] == "success"])
    total_count = len(dataframe)
    success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
    return success_rate, success_count, total_count


def save_results_to_csv(
    dataframes: List[pd.DataFrame], output_path: str, create_directory: bool = True
) -> Optional[pd.DataFrame]:
    """
    Combine multiple DataFrames and save results to CSV file.

    Args:
        dataframes: List of DataFrames to combine
        output_path: Path to save the CSV file
        create_directory: Whether to create output directory if it doesn't exist

    Returns:
        Combined DataFrame if successful, None if no data to save

    Example:
        >>> dfs = [pd.DataFrame({'col': [1, 2]}), pd.DataFrame({'col': [3, 4]})]
        >>> result_df = save_results_to_csv(dfs, 'output/results.csv')
    """
    if not dataframes:
        logger.warning("No results to save")
        return None

    if create_directory:
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    final_df = pd.concat(dataframes, ignore_index=True)
    final_df.to_csv(output_path, index=False)

    success_rate, success_count, total_count = calculate_success_rate(final_df)
    logger.info(f"Successfully saved {len(final_df)} records to {output_path}")
    logger.info(f"Success rate: {success_rate:.2f}% ({success_count}/{total_count})")

    return final_df
