"""
Date manipulation utilities.

This module provides functions for common date operations including
formatting, calculations, and transformations used across the application.
"""

import logging
from datetime import date, datetime, timedelta
from typing import Optional

import pandas as pd

logger = logging.getLogger(__name__)


def format_date_to_iso(date_obj: datetime) -> str:
    """
    Format a datetime object to ISO 8601 format with milliseconds and Z suffix.

    Args:
        date_obj: The datetime object to format

    Returns:
        Formatted date string in ISO 8601 format

    Example:
        >>> dt = datetime(2023, 1, 1, 12, 0)
        >>> format_date_to_iso(dt)
        '2023-01-01T12:00:00.000Z'
    """
    return date_obj.isoformat() + ".000Z"


def calculate_date_from_age(reference_date: date, age_in_years: int, day_offset: int = 0) -> date:
    """
    Calculate a date by subtracting years from a reference date.

    Args:
        reference_date: The reference date to calculate from
        age_in_years: Number of years to subtract
        day_offset: Optional number of days to offset (default: 0)

    Returns:
        Calculated date

    Example:
        >>> ref_date = date(2023, 1, 1)
        >>> calculate_date_from_age(ref_date, 20)
        datetime.date(2003, 1, 1)
    """
    adjusted_date = reference_date - timedelta(days=day_offset)
    return date(adjusted_date.year - age_in_years, adjusted_date.month, adjusted_date.day)


def format_date_string(date_obj: date, format_str: str = "%d/%m/%Y") -> str:
    """
    Format a date object to a string using the specified format.

    Args:
        date_obj: The date object to format
        format_str: The format string (default: "%d/%m/%Y")

    Returns:
        Formatted date string

    Example:
        >>> dt = date(2023, 1, 1)
        >>> format_date_string(dt)
        '01/01/2023'
    """
    return date_obj.strftime(format_str)
