"""
Models for Comparis scraper data structures.

This module provides Pydantic models for handling insurance quote requests and responses
from the Comparis.ch platform. It includes:

Classes:
    - ComparisMotorScraperArguments: Comparis Motor configuration for scraper execution
    - VehicleInput: Vehicle details and specifications
    - DriverInput: Driver personal and license information
    - CoverageInput: Insurance coverage options and preferences
    - InsuranceQuoteRequest: Complete insurance quote request model

The models ensure type safety and data validation while providing
a clean interface for transforming data between the application and
Comparis API formats.
"""

from comparis_scraper.models.comparis_motor_insurance_request import (
    ComparisMotorScraperArguments,
    CoverageInput,
    DriverInput,
    InsuranceQuoteRequest,
    VehicleInput,
)

__all__ = ["ComparisMotorScraperArguments", "VehicleInput", "DriverInput", "CoverageInput", "InsuranceQuoteRequest"]
