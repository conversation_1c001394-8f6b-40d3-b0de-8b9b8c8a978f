"""
Data transformation utilities for Comparis scraper.

This module provides transformation classes and utilities for converting between
different data formats used in the Comparis scraper:
- Converting raw input data from CSV files to structured Pydantic models
- Mapping between internal data representations and Comparis API formats
- Handling data validation and error reporting during transformations
- Providing utility functions for data cleaning and normalization

The transformers handle the complex logic of preparing data for API requests
and processing API responses into usable formats for analysis.
"""

from comparis_scraper.transformers.comparis_motor_insurance_transformer import (
    ComparisMotorInsuranceTransformer,
    TransformationError,
)

__all__ = ["ComparisMotorInsuranceTransformer", "TransformationError"]
