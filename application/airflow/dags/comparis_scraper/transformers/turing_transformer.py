"""
Turing transformer for EFAG motor insurance data.

This module provides transformation logic for converting between raw input data
and the structured format required by the Turing API. It handles:
- Date calculations and formatting
- Value mapping between internal and Turing-specific codes
- Data validation and error handling
- Conversion between DataFrame and JSON formats
"""

import logging
from typing import Dict, Tuple, Optional, List

import numpy as np
import pandas as pd
from pydantic import ValidationError
from collections import abc

from comparis_scraper.constants.turing_mappings import (
    DUMMY_VEHICLE,
    DUMMY_VARIABLES_OTHER,
    mapper_basket_columns_to_priced_elements,
    mapper_basket_columns_to_risk_questions,
    DEFAULT_PRICING_VERSION,
    DEFAULT_PRODUCT_VERSION,
    DEFAULT_PRODUCT_NAME,
    DEFAULT_AGGREGATION_SHEET,
    DISPLACEMENT_COLUMN_MAPPING,
    INTEGER_COLUMNS,
    BO<PERSON>EAN_COLUMNS,
    COVERAGE_IDS,
    INCLUDE_QUOTE_DETAILS_FOR_OUTPUT_MAPPING,
    INCLUDE_ALL_QUOTE_DETAILS_FOR_OUTPUT_MAPPING,
    PRICED_ELEMENTS_CONSIDERED_FOR_OUTPUT_MAPPING,
    TURING_RESPONSE_TOP_LEVEL_STRUCTURE,
    COLUMNS_FOR_FINAL_PREMIUM_RESCALING,
    TURING_OUTPUT_COLUMN_RENAMER
)
from comparis_scraper.transformers.base_efag_transformer import BaseEFAGTransformer, TransformationError
from comparis_scraper.utils.dataframe_utils import convert_columns_to_numeric, map_boolean_columns
from comparis_scraper.utils.validation_utils import validate_required_columns

logger = logging.getLogger(__name__)


class TuringTransformer(BaseEFAGTransformer):
    """
    Transformer for Turing motor insurance data.

    This class provides methods to transform raw input data from various sources
    into the format required by the Turing API. It handles data validation,
    mapping, and formatting to ensure API compatibility.
    """



    @classmethod
    def transform_and_validate(cls, input_data: pd.DataFrame) -> pd.DataFrame:
        """
        Main transformation method with validation.

        Args:
            input_data: DataFrame containing raw input data

        Returns:
            DataFrame with transformed and validated data
        """
        try:
            # Validate required columns
            required_columns = [
                cls.COL_DRIVER_AGE,
                cls.COL_LICENSE_AGE,
                cls.COL_VEHICLE_AGE,
                cls.COL_CRAWLER_ID,
            ]
            validate_required_columns(input_data, required_columns)

            # Calculate age variables
            transformed_data = cls.calculate_age_variables(input_data.copy())

            logger.info("Turing data transformation completed successfully")
            return transformed_data

        except ValidationError as e:
            logger.error(f"Validation error in Turing data transformation: {str(e)}")
            raise TransformationError(f"Data validation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error in Turing data transformation: {str(e)}")
            raise TransformationError(f"Transformation failed: {str(e)}")

    @classmethod
    def transform_batch_data(cls, input_data: pd.DataFrame, aggregation_file_path: str) -> Tuple[Dict, pd.DataFrame]:
        """
        Transform entire dataset to Turing format

        Args:
            input_data: DataFrame containing all input data
            aggregation_file_path: Path to aggregation Excel file

        Returns:
            Dictionary in Turing batch API format with all quote requests
        """
        try:
            # Transform entire dataset using mapper_rl2turing
            output_dict, indicator = cls.mapper_rl2turing(
                input_data,
                DEFAULT_PRICING_VERSION,
                DEFAULT_PRODUCT_VERSION,
                DEFAULT_PRODUCT_NAME,
                aggregation_file_path,
                DEFAULT_AGGREGATION_SHEET
            )

            logger.info(f"Batch transformation successful: {len(output_dict.get('quoteRequests', []))} quote requests generated")
            return output_dict, indicator

        except Exception as e:
            logger.error(f"Error in batch transformation: {str(e)}")
            raise TransformationError(f"Batch transformation failed: {str(e)}")

    @classmethod
    def create_selection_indicator_table(cls, file_path: str, sheet_name: str) -> pd.DataFrame:
        """
        Create selection indicator table from Excel file.

        Args:
            file_path: Path to the Excel file
            sheet_name: Name of the sheet to read

        Returns:
            DataFrame with selection indicators
        """
        try:
            input_data = pd.read_excel(file_path, sheet_name=sheet_name)
            input_data.set_index(input_data.columns.tolist(), inplace=True)
            return input_data.T
        except Exception as e:
            logger.error(f"Error creating selection indicator table: {str(e)}")
            raise TransformationError(f"Failed to create indicator table: {str(e)}")

    @classmethod
    def process_row(cls, rowdata: pd.Series, indicator: pd.DataFrame, rowdata_selector_ra_base: List,
                   quote_requests_base: List, productVersion: str, pricingVersion: str, productname: str) -> None:
        """
        Process a single row for JSON conversion.

        Args:
            rowdata: Series containing row data
            indicator: DataFrame with selection indicators
            rowdata_selector_ra_base: List of risk question keys
            quote_requests_base: List to append quote requests to
            productVersion: Product version
            pricingVersion: Pricing version
            productname: Product name
        """
        try:
            # Filter risk answers based on conditions
            rowdata_selector_ra = rowdata_selector_ra_base.copy()

            # DRV_RESIDENCE_PERMIT must be dropped from answers if DRV_NATIONALITY in ['CH','FL']
            if rowdata['DRV_NATIONALITY'] in ['CH', 'FL']:
                rowdata_selector_ra.remove('DRV_RESIDENCE_PERMIT')

            # Displacement Risk answer must not be submitted if primary question does not require it
            if rowdata['FUEL_TYPE'] == 'ELECTRIC':
                rowdata_selector_ra.remove('DISPLACEMENT')

            # if productname is chmotor_companies or chmotor, drop the risk question LEASING_DURATION, LEASING_START, LEASING_END
            if ((productname == 'CHMOTOR_COMPANIES') | (productname == 'CHMOTOR')):
                rowdata_selector_ra.remove('LEASING_DURATION')
                rowdata_selector_ra.remove('LEASING_START')
                rowdata_selector_ra.remove('LEASING_END')

            # drop risk question payment_frequency for all engines, it's part of the request, not the risk answers
            rowdata_selector_ra.remove('PAYMENT_FREQUENCY')

            # if product name is CHMOTOR_COMPANIES or CHMOTOR_COMPANIES_LEASING, remove risk questions
            if ((productname == 'CHMOTOR_COMPANIES') | (productname == 'CHMOTOR_COMPANIES_LEASING')):
                rowdata_selector_ra.remove('HOLDER_IS_EMPLOYEE')
                rowdata_selector_ra.remove('VEH_OWNER_IS_HOLDER')
                rowdata_selector_ra.remove('MAIN_DRV_IS_HOLDER')

            # If product name is CHMOTOR_COMPANIES or CHMOTOR_COMPANIES_LEASING, rename risk question DRIVERS17_25 to DRIVERS_25
            if ((productname == 'CHMOTOR_COMPANIES') | (productname == 'CHMOTOR_COMPANIES_LEASING')):
                rowdata_selector_ra = [x if x != 'DRIVERS_17_25' else 'DRIVERS_25' for x in rowdata_selector_ra]
                rowdata.rename({'DRIVERS_17_25': 'DRIVERS_25'}, inplace=True)

            # Create risk answers dictionary
            risk_answers = rowdata.loc[rowdata_selector_ra].to_dict()

            # Create priced elements with proper structure
            priced_elements = cls._create_priced_elements(rowdata)

            # Create quote request structure
            quote_request = {
                "referenceId": str(rowdata.name),
                "include": ["QUOTE_DETAILS"],
                "request": {
                    "product": {
                        "id": productname,
                        "version": productVersion
                    },
                    "pricingVersion": pricingVersion,
                    "answers": risk_answers,
                    "operationType": "FIRST",
                    "paymentFrequency": rowdata.get('PAYMENT_FREQUENCY', 'YEARLY'),
                    "volatile": True,
                    "pricedElements": priced_elements
                }
            }

            quote_requests_base.append(quote_request)

            # Update indicator table
            indicator = cls._update_indicator_table(rowdata, indicator)

        except Exception as e:
            logger.error(f"Error processing row {rowdata.name}: {str(e)}")
            # Continue processing other rows even if one fails

    @classmethod
    def _create_priced_elements(cls, rowdata: pd.Series) -> List[Dict]:
        """Create priced elements structure for Turing API."""
        priced_elements = []

        # MTPL
        mtpl_element = {
            "id": COVERAGE_IDS['MTPL'],
            "coverages": cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_MTPL', 0) * 100)
            },
            "selected": rowdata.get('MTPL_is_selected', False)
        }
        priced_elements.append(mtpl_element)

        # MOD
        mod_element = {
            "id": COVERAGE_IDS['MOD'],
            "coverages": [
                {
                    "id": COVERAGE_IDS['COLLISION'],
                    "selected": rowdata.get('Collision_is_selected', False),
                    "deductible": {"value": int(rowdata.get('Deductible_Collision', 0) * 100)}
                },
                {
                    "id": COVERAGE_IDS['PARKED_VEHICLES'],
                    "selected": rowdata.get('ParkedVehicles_is_selected', False)
                },
                {
                    "id": COVERAGE_IDS['PURCHASE_PRICE_IND'],
                    "selected": rowdata.get('PurchasePriceInd_is_selected', False)
                }
            ] + cls._make_mod_addons(rowdata, COVERAGE_IDS['MOD']) + cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_PMOD', 0) * 100)
            },
            "selected": rowdata.get('MOD_is_selected', False)
        }
        priced_elements.append(mod_element)

        # Glass Window
        glass_window_element = {
            "id": COVERAGE_IDS['GLASS_WINDOW'],
            "coverages": cls._make_mod_addons(rowdata, COVERAGE_IDS['GLASS_WINDOW']) + cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_PMOD', 0) * 100)
            },
            "selected": rowdata.get('MOD_is_selected', False)
        }
        priced_elements.append(glass_window_element)

        # Glass Plus
        glass_plus_element = {
            "id": COVERAGE_IDS['GLASS_PLUS'],
            "coverages": cls._make_mod_addons(rowdata, COVERAGE_IDS['GLASS_PLUS']) + cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_PMOD', 0) * 100)
            },
            "selected": rowdata.get('GlassExtension_is_selected', False)
        }
        priced_elements.append(glass_plus_element)

        return priced_elements

    @classmethod
    def _make_gross_negligence(cls, rowdata: pd.Series) -> List[Dict]:
        """Create gross negligence coverage structure."""
        gross_negligence = rowdata.get('GROSS_NEGLIGENCE', '').upper()

        if gross_negligence == 'YES':
            selected = True
            deductible = 0
        elif gross_negligence == 'NO':
            selected = False
            deductible = None
        else:
            selected = None
            deductible = None

        return [{
            'id': COVERAGE_IDS['GROSS_NEGLIGENCE'],
            'deductible': {
                'value': deductible
            },
            'selected': selected
        }]

    @classmethod
    def _make_mod_addons(cls, rowdata: pd.Series, prel_id: str) -> List[Dict]:
        """Create MOD addon coverages structure."""
        # Core special expenses IDs
        spec_exp_ids_core = ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012']
        locks_and_disposal = ['BE056', 'BE008']

        if prel_id == COVERAGE_IDS['MOD']:
            spec_exp_ids = spec_exp_ids_core + locks_and_disposal
        else:
            spec_exp_ids = spec_exp_ids_core

        pers_eff_id = COVERAGE_IDS['PERSONAL_EFFECTS']
        glass_plus = []

        # Personal Effects coverage
        pers_eff = [{
            'id': pers_eff_id,
            'selected': rowdata.get('PersonalEffects_is_selected', False),
            'deductible': {
                'options': None,
                'value': 0
            },
            'sumInsured': {
                'value': int(rowdata.get('PersonalEffects_sumInsured', 0) * 100)
                if rowdata.get('PersonalEffects_is_selected', False) else None
            }
        }]

        # Special Expenses coverages
        spec_exp = [{
            'id': spec_id,
            'selected': rowdata.get('SpecialExpenses_is_selected', False),
            'deductible': {
                'options': None,
                'value': 0
            },
            'sumInsured': {
                'value': int(rowdata.get('SpecialExpenses_sumInsured', 0) * 100)
                if rowdata.get('SpecialExpenses_is_selected', False) else None
            }
        } for spec_id in spec_exp_ids]

        # Glass Plus coverage (only for Glass Plus coverage ID)
        if prel_id == COVERAGE_IDS['GLASS_PLUS']:
            glass_plus = [{
                'id': COVERAGE_IDS['GLASS_PLUS_ADDON'],
                'selected': rowdata.get('GlassExtension_is_selected', False)
            }]

        return spec_exp + pers_eff + glass_plus

    @classmethod
    def _update_indicator_table(cls, rowdata: pd.Series, indicator: pd.DataFrame) -> pd.DataFrame:
        """Update the indicator table with selection indicators."""
        try:
            # Generate a table that indicates selected yes/no for each object, and priced element.
            # This will be used to multiply not selected priced components with 0.
            indicator.loc[rowdata.name, (267, ['P0064', 'P0026'])] = 1  # MTPL always selected
            indicator.loc[rowdata.name, (267, ['DB001'])] = 1 if rowdata['GROSS_NEGLIGENCE'] == 'YES' else 0 if rowdata[
                                                                                                                    'GROSS_NEGLIGENCE'] == 'NO' else np.nan

            indicator.loc[
                rowdata.name, (408, ['P0045', 'P0069', 'P0046', 'P0071', 'P0047', 'P0073', 'P0074', 'P0050'])] = 1 if \
                rowdata['MOD_is_selected'] else 0
            indicator.loc[rowdata.name, (408, ['P0075'])] = 1 if rowdata['Collision_is_selected'] else 0

            indicator.loc[
                rowdata.name, (408, ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012', 'BE056', 'BE008'])] = 1 if \
                (rowdata['SpecialExpenses_is_selected'] & rowdata['MOD_is_selected']) else 0
            indicator.loc[rowdata.name, (409, ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012'])] = 1 if (rowdata[
                                                                                                                   'SpecialExpenses_is_selected'] &
                                                                                                               rowdata[
                                                                                                                   'MOD_is_selected']) else 0
            indicator.loc[rowdata.name, (410, ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012'])] = 1 if (rowdata[
                                                                                                                   'SpecialExpenses_is_selected'] &
                                                                                                               rowdata[
                                                                                                                   'GlassExtension_is_selected']) else 0

            indicator.loc[rowdata.name, (408, ['BE002'])] = 1 if (
                    rowdata['PersonalEffects_is_selected'] & rowdata['MOD_is_selected']) else 0
            indicator.loc[rowdata.name, (409, ['BE002'])] = 1 if (
                    rowdata['PersonalEffects_is_selected'] & rowdata['MOD_is_selected']) else 0
            indicator.loc[rowdata.name, (410, ['BE002'])] = 1 if (
                    rowdata['PersonalEffects_is_selected'] & rowdata['GlassExtension_is_selected']) else 0

            indicator.loc[rowdata.name, (408, ['P0084'])] = 1 if rowdata['ParkedVehicles_is_selected'] else 0
            indicator.loc[rowdata.name, (408, ['DB001'])] = 1 if (
                    (rowdata['GROSS_NEGLIGENCE'] == 'YES') & rowdata['MOD_is_selected']) else 0 if (
                    (rowdata['GROSS_NEGLIGENCE'] == 'NO') | (rowdata['GROSS_NEGLIGENCE'] == 'YES') & (
                not rowdata['MOD_is_selected'])) else np.nan

            indicator.loc[rowdata.name, (409, ['P0072'])] = 1 if rowdata['MOD_is_selected'] else 0
            indicator.loc[rowdata.name, (409, ['DB001'])] = 1 if (
                    (rowdata['GROSS_NEGLIGENCE'] == 'YES') & rowdata['MOD_is_selected']) else 0 if (
                    (rowdata['GROSS_NEGLIGENCE'] == 'NO') | (rowdata['GROSS_NEGLIGENCE'] == 'YES') & (
                not rowdata['MOD_is_selected'])) else np.nan

            indicator.loc[rowdata.name, (410, ['P0072'])] = 1 if rowdata['GlassExtension_is_selected'] else 0
            indicator.loc[rowdata.name, (410, ['DB001'])] = 1 if (
                    (rowdata['GROSS_NEGLIGENCE'] == 'YES') & rowdata['GlassExtension_is_selected']) else 0 if (
                    (rowdata['GROSS_NEGLIGENCE'] == 'NO') | (rowdata['GROSS_NEGLIGENCE'] == 'YES') & (
                not rowdata['GlassExtension_is_selected'])) else np.nan

            indicator.loc[rowdata.name, (408, ['DB005'])] = 1 if rowdata['PurchasePriceInd_is_selected'] else 0

            return indicator

        except Exception as e:
            logger.error(f"Error updating indicator table for row {rowdata.name}: {str(e)}")

    @classmethod
    def _get_default_value_for_column(cls, column_name: str):
        """Get appropriate default value for a column based on its expected type."""
        if column_name in INTEGER_COLUMNS:
            return 0
        elif column_name in BOOLEAN_COLUMNS:
            return False
        elif 'DATE' in column_name.upper() or 'DOB' in column_name.upper():
            return '01/01/2000'
        else:
            return ''  # String default

    @classmethod
    def process_response(cls, response_data: Dict, request_data: Dict) -> Dict:
        """
        Process Turing API response data.

        Args:
            response_data: Raw response from Turing API
            request_data: Original request data

        Returns:
            Processed response data
        """
        try:
            # Extract key information from response
            processed_response = {
                "crawler_id": request_data.get("CrawlerID", "unknown"),
                "status": "success",
                "quotes": []
            }

            # Process quotes if available
            if "quotes" in response_data:
                for quote in response_data["quotes"]:
                    quote_info = {
                        "reference_id": quote.get("referenceId"),
                        "product_id": quote.get("quote", {}).get("product", {}).get("id"),
                        "pricing_version": quote.get("quote", {}).get("pricingVersion"),
                        "status": quote.get("quote", {}).get("status"),
                        "currency": quote.get("quote", {}).get("currency"),
                        "total_premiums": quote.get("quote", {}).get("totalPremiums", {}),
                        "client_error": quote.get("clientError"),
                        "server_error": quote.get("serverError")
                    }
                    processed_response["quotes"].append(quote_info)

            return processed_response

        except Exception as e:
            logger.error(f"Error processing Turing response: {str(e)}")
            return {
                "crawler_id": request_data.get("CrawlerID", "unknown"),
                "status": "error",
                "error": str(e),
                "quotes": []
            }

    @classmethod
    def map_string_columns(cls, output_ra: pd.DataFrame, productversion: str) -> None:
        """Map string columns to appropriate values."""
        output_ra['GROSS_NEGLIGENCE'] = output_ra['GROSS_NEGLIGENCE'].str.upper()
        output_ra['HOLDER_IS_EMPLOYEE'] = output_ra['HOLDER_IS_EMPLOYEE'].str.upper()
        output_ra['VEH_OWNER_IS_HOLDER'] = output_ra['VEH_OWNER_IS_HOLDER'].str.upper()
        output_ra['USUAL_PARK_LOCATION'] = output_ra['USUAL_PARK_LOCATION'].str.upper()
        output_ra['MAIN_DRV_IS_HOLDER'] = output_ra['MAIN_DRV_IS_HOLDER'].str.upper()

        output_ra['DRIVERS_17_25'] = output_ra['DRIVERS_17_25'].map(
            {'0': 'NO', '1': 'YES', '2': 'YES', '3P': 'YES'}) if productversion in ['CHMOTOR_COMPANIES',
                                                                                    'CHMOTOR_COMPANIES_LEASING'] else \
            output_ra['DRIVERS_17_25']
        output_ra['FUEL_TYPE'] = output_ra['DISPLACEMENT'].fillna(0).apply(
            lambda x: 'ELECTRIC' if x == 0 else 'PETROL_CAT')

    @classmethod
    def convert_table_to_json(cls, output_ra: pd.DataFrame, aggregationFilePath: str,
                              aggregation_sheet_name: str, mapper_risk_questions: Dict,
                              productVersion: str, pricingVersion: str, productname: str) -> Tuple[Dict, pd.DataFrame]:
        """Convert DataFrame to JSON format for Turing API."""
        # Key Step: Create the Turing-readable JSON format for the /quote/batch request.
        # This conversion is product-specific and not generalizable.
        # The idea is that the input comes in rows of a pandas table and iterates through the rows.
        quote_requests_base = []
        indicator = cls.create_selection_indicator_table(aggregationFilePath, aggregation_sheet_name)
        rowdata_selector_ra_base = list(mapper_risk_questions.keys())

        output_ra.apply(cls.process_row, axis=1, args=(
            indicator, rowdata_selector_ra_base, quote_requests_base, productVersion, pricingVersion, productname))
        return {'quoteRequests': quote_requests_base}, indicator

    @classmethod
    def mapper_rl2turing(cls, input: pd.DataFrame, pricingVersion: str, productVersion: str,
                         productname: str, aggregationFilePath: str, aggregation_sheet_name: str) -> Tuple[
        Dict, pd.DataFrame]:
        """
        Transform risk language data to Turing format.

        Args:
            input: DataFrame containing raw input data
            pricingVersion: Version of the pricing engine
            productVersion: Version of the product
            productname: Name of the product
            aggregationFilePath: Path to the aggregation file
            aggregation_sheet_name: Name of the aggregation sheet

        Returns:
            Tuple containing the transformed data in Turing format and the indicator DataFrame
        """
        try:
            # Add dummy data to input, avoiding duplicates
            input_data = pd.concat([input, pd.DataFrame(DUMMY_VEHICLE, index=input.index),
                               pd.DataFrame(DUMMY_VARIABLES_OTHER, index=input.index)], axis=1)

            # Calculate age variables
            cls.calculate_age_variables(input_data)

            # Write Output Table for Risk Answer component of request
            output_ra = pd.DataFrame()
            [output_ra.insert(0, y, input_data[x]) for y, x in mapper_basket_columns_to_risk_questions.items()]

            # Add Elements for Priced Elements to Output Table. Subset later on columns
            [output_ra.insert(0, y, input_data[x]) for y, x in mapper_basket_columns_to_priced_elements.items()]

            output_ra = map_boolean_columns(output_ra, [
                'MTPL_is_selected',
                'MOD_is_selected',
                'Collision_is_selected',
                'ParkedVehicles_is_selected',
                'GlassExtension_is_selected',
                'SpecialExpenses_is_selected',
                'PersonalEffects_is_selected',
                'PurchasePriceInd_is_selected'
            ])
            output_ra = convert_columns_to_numeric(output_ra, ['Deductible_PMOD', 'Deductible_Collision', 'Deductible_MTPL', 'SpecialExpenses_sumInsured',
                              'PersonalEffects_sumInsured', 'VEH_EMPTY_WEIGHT', 'VEH_TOTAL_WEIGHT', 'VEH_POWER',
                              'DISPLACEMENT'])
            output_ra = convert_columns_to_numeric(output_ra, ['ACC_CATALOG_PRICE', 'VEH_CATALOG_PRICE', 'LEASING_DURATION'], float)
            cls.map_string_columns(output_ra, productname)

            # Rename Input column names
            renamer_input = mapper_basket_columns_to_risk_questions.copy()
            renamer_input.update(mapper_basket_columns_to_priced_elements)
            renamer_input = {y: x for x, y in renamer_input.items()}
            input.rename(columns=renamer_input, inplace=True)

            return cls.convert_table_to_json(
                output_ra,
                aggregationFilePath,
                aggregation_sheet_name,
                mapper_basket_columns_to_risk_questions,
                productVersion,
                pricingVersion,
                productname
            )
        except Exception as e:
            logger.error(f"Error processing Turing response: {str(e)}")

    @staticmethod
    def normalize_json_response(r):
        normalized_json = pd.json_normalize(r.json(), 'quotes')
        normalized_json.set_index('referenceId', inplace=True)

        # Assign clientError.violations column if it does not exist
        if 'clientError.violations' not in normalized_json:
            normalized_json['clientError.violations'] = None

        # Assign serverError.message column if it does not exist
        if 'serverError.message' not in normalized_json:
            normalized_json['serverError.traceId'] = None

        normalized_json = normalized_json[
            [x for x, y in TURING_RESPONSE_TOP_LEVEL_STRUCTURE.items() if y['drop'] is False]].rename(
            columns={x: y['newname'] for x, y in TURING_RESPONSE_TOP_LEVEL_STRUCTURE.items() if y['drop'] is False},
            inplace=False)
        return normalized_json

    @staticmethod
    def getPricedElements():
        pricedElements_HEMS = {'ContentsAtHome': 242, 'ContentsAway': 243, 'BuildingGlass': 264, 'FurnitureGlass': 265,
                               'PersonalLiability': 257, 'PL LicensedTPV': 258, 'PL Tenant': 259}
        pricedElements_EFAG = {'MTPL': 267, 'MOD': 408, 'GlassWindow': 409, 'GlassPlus': 410}
        pricedElements_INDEPM = {'MTPL': 267, 'MTPL Own Vehicle': 412, 'MOD': 408, 'GlassWindow': 409, 'Passenger': 411}
        pricedElements_check_building = {'GlassAddon': 413, 'Burglary': 414, 'Animal': 415, 'Building': 416,
                                         'Waste Water': 417}
        pricedElements_INDEPHH = {'Building': 401, 'Glass': 402, 'Content': 403, 'Electronics': 404,
                                  'ContentAllrisk': 405,
                                  'PL Private': 406, 'PL Tenant': 407}  # ! 416 is building and elementary

        return {'HEMSAKER': pricedElements_HEMS,
                'CHMOTOR': pricedElements_EFAG,
                'CHMOTOR_COMPANIES': pricedElements_EFAG,
                'CHMOTOR_LEASING': pricedElements_EFAG,
                'CHMOTOR_COMPANIES_LEASING': pricedElements_EFAG,
                'INDEPENDER_MOTOR': pricedElements_INDEPM,
                'CHECK24_BUILDING': pricedElements_check_building,
                'INDEPENDER_HOUSEHOLD': pricedElements_INDEPHH}.get(DEFAULT_PRODUCT_NAME)

    @classmethod
    def flatten(cls,d, parent_key='', sep='_'):
        items = []
        for k, v in d.items():
            new_key = parent_key + sep + k if parent_key else k
            if isinstance(v, abc.MutableMapping):
                items.extend(cls.flatten(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)
    @classmethod
    def process_row_out(cls, rowdata, row, pricedEl):
        pricedElements = cls.getPricedElements()
        rowdata = pd.DataFrame(rowdata).T
        rowdata.columns = pd.MultiIndex.from_tuples([(x, 'INFO', 'INFO', 'INFO') for x in rowdata.columns])

        l2 = [
            a[['MainObject', 'id', 'Row', 'quoteDetails'] + [x for x in a.columns if ('premiums_finalPremiums' in x) & (
                    'Rounded' not in x)]].set_index(['MainObject', 'id', 'Row'], inplace=False)
            for a in [
                pd.DataFrame([{**cls.flatten(z), **{'MainObject': w}, **{'Row': row}} for z in
                              {x['id']: x['coverages'] for x in pricedEl if
                               int(x['id']) in list(pricedElements.values())}[w]])
                for w in
                {x['id']: x['coverages'] for x in pricedEl if int(x['id']) in list(pricedElements.values())}
            ]
        ]

        # Divide final premiums by 100 as per Turing convention
        for tmp in l2:
            tmp[COLUMNS_FOR_FINAL_PREMIUM_RESCALING] = tmp[COLUMNS_FOR_FINAL_PREMIUM_RESCALING].div(100)

        if INCLUDE_QUOTE_DETAILS_FOR_OUTPUT_MAPPING:
            quotD = pd.DataFrame()
            for ctr in range(0, len(l2)):
                tmp_peril = pd.DataFrame()
                for ctr_peril in range(0, len(l2[ctr].index)):
                    if (l2[ctr]['quoteDetails'].iloc[ctr_peril] != []):

                        if INCLUDE_ALL_QUOTE_DETAILS_FOR_OUTPUT_MAPPING == False:
                            tmp = pd.json_normalize([x for x in l2[ctr]['quoteDetails'].iloc[ctr_peril] if
                                                     x['id'] in PRICED_ELEMENTS_CONSIDERED_FOR_OUTPUT_MAPPING]).set_index(
                                'id')[['value']].T
                        else:
                            tmp = \
                                pd.json_normalize([x for x in l2[ctr]['quoteDetails'].iloc[ctr_peril]]).set_index('id')[
                                    ['value']].T
                        tmp.index = l2[ctr].index[[ctr_peril]]
                    else:
                        tmp = pd.DataFrame()
                    tmp_peril = pd.concat([tmp_peril, tmp], ignore_index=False)
                quotD = pd.concat([quotD, tmp_peril])

            l2 = pd.concat(*[l2]).drop('quoteDetails', axis=1).merge(quotD, left_index=True, right_index=True,
                                                                     how='left').unstack(level=[0, 1])
        else:
            l2 = pd.concat(*[l2]).drop('quoteDetails', axis=1).unstack(level=[0, 1])

        l2.columns = pd.MultiIndex.from_tuples([('PREMIUMINFO', *x) for x in l2.columns.to_list()])

        return pd.concat([rowdata, l2], axis=1)

    @classmethod
    def create_output_tbl(cls, normalized_json):
        output_tbl = []

        for row, rowdata in normalized_json.iterrows():
            if rowdata['ProductId'] is not np.nan:
                pricedEl = rowdata.pop('PricedElements')
                processed_row = cls.process_row_out(rowdata, row, pricedEl)
                output_tbl.append(processed_row)
        return pd.concat(output_tbl, axis=0)

    @staticmethod
    def create_final_output_df(output_tbl, indicator):
        # Intermediate result: Quote and Tariffinfo
        metadata_table = output_tbl.loc[:, (slice(None), 'INFO')]
        metadata_table.columns = metadata_table.columns.get_level_values(0)

        # Process Full Output Table for Downstream Use
        output_tbl = output_tbl.reorder_levels([0, 2, 3, 1], axis=1)
        output_prem = output_tbl.loc[:, ('PREMIUMINFO', slice(None))].droplevel(0, axis=1)
        indicator.columns = pd.MultiIndex.from_tuples(
            [(*[str(y) for y in x],) for x in indicator.columns])  # Convert object id to string
        indicator.sort_index(axis=1, inplace=True)
        indicator_broad = pd.DataFrame(columns=output_prem.columns, index=indicator.index)

        for x in output_prem.columns:
            indicator_broad[x] = indicator.loc[:, (x[0], indicator.columns.isin(x[:2], level=1))].squeeze()

        output_prem = output_prem.multiply(indicator_broad)
        output_prem.columns = pd.MultiIndex.from_tuples(
            [x + indicator.loc[:, (*x[0:2],)].columns[0] for x in output_prem.columns])

        # iterate over number of aggregation levels
        aggr_output_prem = pd.DataFrame()
        for ctr in range(4, output_prem.columns.nlevels):
            tmp = output_prem.T.groupby(level=[2, ctr]).sum().T
            tmp.columns = pd.MultiIndex.from_tuples([(tuple(['AGG', str(ctr - 4)]) + (*x,)) for x in tmp.columns])
            aggr_output_prem = pd.concat([aggr_output_prem, tmp], axis=1)

        # After multiplication and aggregation, convert to nan to avoid wrong averaging along profile dimension
        aggr_output_prem = aggr_output_prem.replace({0: np.nan})

        # Rename some columns for better readability
        aggr_output_prem.columns = pd.MultiIndex.from_tuples(
            [tuple([TURING_OUTPUT_COLUMN_RENAMER.get(x, x) for x in y]) for y in aggr_output_prem.columns])

        # Flatten MultiIndex
        aggr_output_prem.columns = ['-'.join(x) for x in aggr_output_prem.columns]

        return aggr_output_prem.merge(metadata_table, left_index=True, right_index=True)

    @classmethod
    def mapper_TuringOutputPremia(cls, r, indicator):
        normalized_json = cls.normalize_json_response(r)
        output_tbl = cls.create_output_tbl(normalized_json)
        ErrorTable = normalized_json[['ClientError', 'ServerError']].assign(
            comparison=(pd.notna((normalized_json['ClientError']))) | (pd.notna(normalized_json['ServerError']))).apply(
            lambda x: x[['ClientError', 'ServerError']].to_dict() if x['comparison'] is True else np.nan, axis=1)
        ErrorTable.name = 'Errors'
        output_tbl = output_tbl.reindex(ErrorTable.index)
        output_tbl.update({('ClientError', 'INFO', 'INFO', 'INFO'): ErrorTable})

        final_output_df = cls.create_final_output_df(output_tbl, indicator)

        return final_output_df