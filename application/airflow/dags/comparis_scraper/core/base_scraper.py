"""
Base scraper module for Comparis web scraping.

This module provides the foundation for all Comparis scrapers with common functionality:
- HTTP request handling with retry logic and exponential backoff
- Concurrency control via semaphores
- Header management with rotating user agents
- Error handling and logging

The ComparisBaseScraper abstract base class should be extended by specific scrapers
that implement the actual scraping logic for different Comparis services.
"""

import asyncio
import logging
import random
from abc import ABC
from typing import Any, Dict, List, Optional

import boto3
import httpx

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)
logging.getLogger("httpx").setLevel(logging.WARNING)


class ComparisBaseScraper(ABC):
    def __init__(
        self,
        session: httpx.AsyncClient,
        semaphore: asyncio.Semaphore,
        max_retries: int = 10,
        user_agents: Optional[List[str]] = None,
    ):
        """
        Initialize the ComparisBaseScraper.

        Args:
            session: HTTP session for making requests.
            semaphore: Semaphore for controlling concurrency.
            max_retries: Maximum number of retries for requests.
            user_agents: List of user agents for requests.
        """
        self.session = session
        self.semaphore = semaphore
        self.max_retries = max_retries
        self.user_agents = user_agents or [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
        ]
        self.s3_client = boto3.client("s3")

    @staticmethod
    def exponential_backoff(retry_count: int, max_delay: float = 120.0, jitter: float = 5) -> float:
        """
        Implements exponential backoff with jitter.

        Args:
            retry_count (int): The retry_count for exponential backoff.
            max_delay (float, optional): The maximum delay. Defaults to 120.0.
            jitter (float, optional): The amount of jitter to add to the delay. Defaults to 5.

        Returns:
            float: The next delay to wait.
        """
        backoff_time = min((2 ** (retry_count - 1)) * random.randrange(5, 10), max_delay) + jitter
        return backoff_time

    def _get_headers(self, additional_headers: Optional[Dict] = None) -> Dict:
        """Generate headers with random user agent."""
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "user-agent": random.choice(self.user_agents),
            "content-type": "application/json",
        }
        if additional_headers:
            headers.update(additional_headers)
        return headers

    async def _make_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None,
        timeout: int = 60,
        retry_count: int = 0,
    ) -> Dict[str, Any]:
        """Make HTTP request with retry logic."""
        async with self.semaphore:
            try:
                response = await self.session.request(
                    method=method, url=url, json=data, headers=headers, timeout=timeout
                )
                response.raise_for_status()
                return response.json()

            except httpx.HTTPError as e:
                logger.error(f"Request failed: {str(e)}")
                raise

    async def get(self, url: str, headers: Optional[Dict] = None, timeout: int = 60) -> Dict[str, Any]:
        """Make GET request."""
        headers = self._get_headers(headers)
        return await self._make_request("GET", url, headers=headers, timeout=timeout)

    async def post(self, url: str, data: Dict, headers: Optional[Dict] = None) -> Dict:
        """Make POST request."""
        headers = self._get_headers(headers)
        return await self._make_request("POST", url, data=data, headers=headers)
