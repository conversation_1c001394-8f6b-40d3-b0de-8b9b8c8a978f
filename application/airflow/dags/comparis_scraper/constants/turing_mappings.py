"""
Mapping dictionaries for Turing data transformation.

This module contains mapping dictionaries used to transform between
internal data representations and Turing-specific codes and values.
"""
import datetime

# Turing API Configuration Constants
# Output Mapping Config: Include premium components besides the final premia
DEFAULT_PRICING_VERSION = "31"
DEFAULT_PRODUCT_VERSION = "4"
DEFAULT_PRODUCT_NAME = "CHMOTOR"
DEFAULT_AGGREGATION_SHEET = "relations"
INCLUDE_QUOTE_DETAILS_FOR_OUTPUT_MAPPING = False
INCLUDE_ALL_QUOTE_DETAILS_FOR_OUTPUT_MAPPING = False
PRICED_ELEMENTS_CONSIDERED_FOR_OUTPUT_MAPPING = ["ALAE", "BURNING_COST_WITHOUT_ROUNDING"]
OUTPUT_MAPPING_CONFIGURATION = {
    "includeQuoteDetailsForOutputMapping": False,
    "includeAllQuoteDetailsForOutputMapping": False,
    "pricedElementsConsideredForOutputMapping": ["ALAE", "BURNING_COST_WITHOUT_ROUNDING"],
    "aggregation_sheet_name": "relations",
    "productname": "CHMOTOR",
    "productVersion": 4,
    "pricingVersion": "31",
}
# Column Mapping Constants
DISPLACEMENT_COLUMN_MAPPING = {"What is the vehicles displacement (Cubic capacity)?": "DISPLACEMENT"}

# Column Type Definitions for Default Values
INTEGER_COLUMNS = [
    "Deductible_PMOD",
    "Deductible_Collision",
    "Deductible_MTPL",
    "ACC_CATALOG_PRICE",
    "VEH_EMPTY_WEIGHT",
    "VEH_TOTAL_WEIGHT",
    "DISPLACEMENT",
    "VEH_POWER",
    "VEH_CATALOG_PRICE",
    "SpecialExpenses_sumInsured",
    "PersonalEffects_sumInsured",
    "LEASING_DURATION",
    "VEH_SEATS",
    "VEH_DOORS",
]

BOOLEAN_COLUMNS = [
    "MTPL_is_selected",
    "MOD_is_selected",
    "Collision_is_selected",
    "ParkedVehicles_is_selected",
    "GlassExtension_is_selected",
    "SpecialExpenses_is_selected",
    "PersonalEffects_is_selected",
    "PurchasePriceInd_is_selected",
]

# Turing-specific coverage constants
SPEC_EXP_IDS_CORE = ["BE057", "BE001", "BE007", "BE051", "BE050", "BE012"]
LOCKS_AND_DISPOSAL = ["BE056", "BE008"]

# Coverage IDs
COVERAGE_IDS = {
    "MTPL": "267",
    "MOD": "408",
    "GLASS_WINDOW": "409",
    "GLASS_PLUS": "410",
    "GROSS_NEGLIGENCE": "DB001",
    "COLLISION": "P0075",
    "PARKED_VEHICLES": "P0084",
    "PURCHASE_PRICE_IND": "DB005",
    "PERSONAL_EFFECTS": "BE002",
    "GLASS_PLUS_ADDON": "P0072",
}
# Dummy data for required fields
DUMMY_VEHICLE = {
    "VEHICLE_TYPE": "PASSENGER_CAR",
    "VEH_MODEL": "NX",
    "MODEL_TYPE": "LEXUS NX",
    "ENERGY_EFFICIENCY": "G",
    "GEAR_TYPE": "CONTINUOUS",
    "VEH_BRAND": "Lexus",
    "VEH_SEATS": 5,
    "VEH_DOORS": 5,
    "VEHICLE_BODY_SHAPE": "SEDAN",
    "VEH_EMPTY_WEIGHT": 1000,
    "USUAL_PARK_LOCATION": "YES",
}
DUMMY_VARIABLES_OTHER = {
    "POLICY_START": (datetime.date.today() + datetime.timedelta(2)).strftime("%d/%m/%Y"),
    "VEH_OWNER_IS_HOLDER": "YES",
    "MAIN_DRV_IS_HOLDER": "YES",
}

mapper_basket_columns_to_risk_questions = {
    "HOLDER_IS_EMPLOYEE": "HOLDER_IS_EMPLOYEE",
    "DRV_RESIDENCE_PERMIT": "MD Residence Type",
    "VEHICLE_TYPE": "VEHICLE_TYPE",
    "VEH_POWER": "What is the vehicles power (in kw)?",
    "VEH_MODEL": "VEH_MODEL",
    "MODEL_TYPE": "MODEL_TYPE",
    "ACC_CATALOG_PRICE": "What is the catalog price of the vehicle accessories?",
    "VEH_OWNER_IS_HOLDER": "VEH_OWNER_IS_HOLDER",
    "DRV_NATIONALITY": "MD Nation",
    "POLICY_START": "POLICY_START",
    "INTENDED_USE": "What is the intended use of the vehicle?",
    "VEH_EMPTY_WEIGHT": "VEH_EMPTY_WEIGHT",
    "VEH_TOTAL_WEIGHT": "What is the vehicles total weight (in kg)?",
    "GROSS_NEGLIGENCE": "GrossNegligence",
    "DRIVERS_17_25": "How many drivers between 17 years and 25 years are in the same household with the main driver?",
    "VEH_LEASING": "How is the vehicle purchased?",
    "DRV_LICENSE_DATE": "When has the main driver obtained the driving license (date)?",
    "DRV_ZIP_CODE": "What's the ZIP code of the main driver's residence?",
    "MILEAGE": "Annual Mileage in kilometres",
    "USUAL_PARK_LOCATION": "USUAL_PARK_LOCATION",
    "PAYMENT_FREQUENCY": "PAYMENT_FREQUENCY",
    "MAIN_DRV_IS_HOLDER": "MAIN_DRV_IS_HOLDER",
    "DRV_DOB": "What is the main driver date of birth?",
    "MATRICULATION": "When is the 1st matriculation of the vehicle on the market?",
    "DISPLACEMENT": "DISPLACEMENT",
    "ENERGY_EFFICIENCY": "ENERGY_EFFICIENCY",
    "VEH_CATALOG_PRICE": "What is the catalog price of the vehicle?",
    "DRV_CITY": "What is the city of the main driver's residence?",
    "DRV_GENDER": "What is the main driver gender?",
    "GEAR_TYPE": "GEAR_TYPE",
    "VEH_BRAND": "VEH_BRAND",
    "VEH_SEATS": "VEH_SEATS",
    "VEH_DOORS": "VEH_DOORS",
    "FUEL_TYPE": "FUEL_TYPE",
    "VEHICLE_BODY_SHAPE": "VEHICLE_BODY_SHAPE",
    "LEASING_DURATION": "LEASING_DURATION",
    "LEASING_START": "LEASING_START",
    "LEASING_END": "LEASING_END",
}

mapper_basket_columns_to_priced_elements = {
    "Deductible_PMOD": "What is the Deductible for Partial MOD?",
    "Deductible_Collision": "What is the Deductible for Collision",
    "Deductible_MTPL": "What is the Deductible for MTPL",
    "MTPL_is_selected": "MTPL",
    "MOD_is_selected": "MOD",
    "Collision_is_selected": "Collision",
    "ParkedVehicles_is_selected": "ParkedVehicles",
    "GlassExtension_is_selected": "GlassExtension",
    "SpecialExpenses_is_selected": "SpecialExpenses",
    "PersonalEffects_is_selected": "PersonalEffects",
    "GrossNegligence_is_selected": "GrossNegligence",
    "SpecialExpenses_sumInsured": "SpecialExpensesSI",
    "PersonalEffects_sumInsured": "PersonalEffSI",
    "PurchasePriceInd_is_selected": "PurchasePriceIndemnification",
}

TURING_RESPONSE_TOP_LEVEL_STRUCTURE = {
    "quote.product.id": {"newname": "ProductId", "drop": False},
    "quote.product.version": {"newname": "ProductVersion", "drop": False},
    "quote.pricingVersion": {"newname": "PricingVersion", "drop": False},
    "quote.status": {"newname": "Status", "drop": True},
    "quote.operationType": {"newname": "QuoteOperationType", "drop": True},
    "quote.currency": {"newname": "QuoteCurrency", "drop": True},
    "quote.pricedElements": {"newname": "PricedElements", "drop": False},
    "quote.paymentFrequency": {"newname": "QuotePaymentFrequency", "drop": False},
    "quote.paymentFrequencyOptions": {"newname": "paymentFrequencyOptions", "drop": True},
    "quote.totalPremiums.finalPremiumsBeforeTaxesRounded._12M": {"newname": "TotPremFPBFTX_12M", "drop": True},
    "quote.totalPremiums.finalPremiumsBeforeTaxesRounded.forSelectedPaymentFrequency": {
        "newname": "TotPremFPBFTX_SPF",
        "drop": True,
    },
    "quote.totalPremiums.finalPremiumsBeforeTaxesRounded.forTotalPeriod": {
        "newname": "TotPremFPBFTX_TP",
        "drop": False,
    },
    "quote.totalPremiums.taxesRounded._12M": {"newname": "TotPremTX_12M", "drop": True},
    "quote.totalPremiums.taxesRounded.forSelectedPaymentFrequency": {"newname": "TotPremTX_SPF", "drop": True},
    "quote.totalPremiums.finalPremiums._12M": {"newname": "TotPremFPAFTX_12M", "drop": True},
    "quote.totalPremiums.finalPremiums.forSelectedPaymentFrequency": {"newname": "TotPremFPAFTX_SPF", "drop": True},
    "quote.totalPremiums.finalPremiums.forTotalPeriod": {"newname": "TotPremFPAFTX_TP", "drop": False},
    "clientError.violations": {"newname": "ClientError", "drop": False},
    "serverError.traceId": {"newname": "ServerError", "drop": False},
}


COLUMNS_FOR_FINAL_PREMIUM_RESCALING = [
    "premiums_finalPremiums_forSelectedPaymentFrequency",
    "premiums_finalPremiums_forTotalPeriod",
]

TURING_OUTPUT_COLUMN_RENAMER = {
    "premiums_finalPremiums__12M": "EndCustPrem",
    "premiums_finalPremiumsBeforeTaxes__12M": "GWP",
    "premiums_finalPremiumsBeforeTaxes_forSelectedPaymentFrequency": "GWP_SelPayFreq",
    "premiums_finalPremiumsBeforeTaxes_forTotalPeriod": "GWP_TotPer",
    "premiums_finalPremiums_forSelectedPaymentFrequency": "EndCustPrem_SelPayFreq",
    "premiums_finalPremiums_forTotalPeriod": "EndCustPrem_TotPer",
}
