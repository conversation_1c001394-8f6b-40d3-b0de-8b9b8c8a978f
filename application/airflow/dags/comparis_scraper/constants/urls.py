"""
URL constants for Comparis API endpoints.

This module contains base URLs and endpoint paths for the various
Comparis APIs used by the scraper.
"""

# Base URLs for different Comparis services
COMPARIS_MOTOR_BASE_URL = "https://en.comparis.ch/autoversicherung/api"

# Endpoint paths can be added here as needed
MOTOR_SAVE_INPUT_PATH = "/v2/input/saveinputv3"
MOTOR_RESULTS_PATH = "/v1/result/redesignresult"
XCALLER_INTERNAL = {"x-caller": "internal"}
TURING_BASE_URL = "https://prep.d.eu1.caramelspec.com/turing/api/v2/quote/batch"
