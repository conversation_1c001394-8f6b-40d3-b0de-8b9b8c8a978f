"""
Constants and configuration values for the Comparis scraper.

This module provides centralized access to constants, mapping dictionaries,
and configuration values used throughout the Comparis scraper package.
"""

# Import all mappings to make them available at the package level
from comparis_scraper.constants.mappings import *
from comparis_scraper.constants.urls import *

__all__ = [
    # Mappings
    "DEDUCTIBLE_PMOD_MAP",
    "DEDUCTIBLE_COLLISION_MAP",
    "DEDUCTIBLE_MTPL_MAP",
    "GENDER_MAP",
    "USAGE_MAP",
    "GARAGE_MAP",
    "MILEAGE_MAP",
    "PURCHASE_MAP",
    "RESIDENCE_MAP",
    "BOOLEAN_MAP",
    # URLs
    "COMPARIS_MOTOR_BASE_URL",
]
