"""
Mapping dictionaries for Comparis data transformation.

This module contains mapping dictionaries used to transform between
internal data representations and Comparis-specific codes and values.
"""

# Deductible mappings
DEDUCTIBLE_PMOD_MAP = {500: 0, 700: None, 800: 300, 1000: 500, 1500: 1000, 2000: None}
DEDUCTIBLE_COLLISION_MAP = {500: None, 800: None, 1000: 500, 1500: 1000, 2500: 2000, 5500: 5000}
DEDUCTIBLE_MTPL_MAP = {0: 0, 500: 0, 1000: 0, 1500: 0, 2000: 0, 5000: 0}

# Demographic and vehicle mappings
GENDER_MAP = {"F": 1, "M": 2}
USAGE_MAP = {"PRIVATE": 1, "PRIV_COMMUTE": 4, "PRIV_COMMUTE_BUS": 7}
GARAGE_MAP = {"WORK": 2, "HOME": 1, "HOME_WORK": 3, "NO": 4}
MILEAGE_MAP = {"0_5": 4000, "5_15": 10000, "15": 20000}
PURCHASE_MAP = {"CASH": False, "LEASING": True}
RESIDENCE_MAP = {"B": 2, "C": 3, "NOT_APPLICABLE": 0, "OTHER": 4}
BOOLEAN_MAP = {"Yes": True, "No": False, "YES": True, "NO": False}
