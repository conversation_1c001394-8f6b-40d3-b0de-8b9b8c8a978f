# Custom dbt Macros

This directory contains custom macros for the price-crawler dbt project.

## Available Macros

### `generate_schema_name`

Customizes schema naming convention by appending the custom schema name to the target schema.

### `iceberg_utils`

Contains macros for managing Iceberg tables in Athena:

#### `delete_iceberg_partition`

Deletes a specific partition from an Iceberg table if it exists.

**Usage:**

```sql

-- model definition
{{ config(
    materialized='incremental',
    incremental_strategy='append',
    file_format='parquet',
    partitioned_by=['crawler_run_date'],
    table_type='iceberg',
    on_schema_change='append_new_columns',
    pre_hook="""
      {% if is_incremental() %}
        {{ delete_iceberg_partition(
          table_name=this,
          partition_column='crawler_run_date',
          partition_value=var('crawler_run_date')
        ) }}
      {% endif %}
    """
) }}


SELECT * FROM ...
```
Run your dbt model with the partition value:

```bash
dbt run --models my_model_name --vars '{"crawler_run_date": "2025-01-01"}'
```

This will:
- Delete the partition for '2025-01-01' if it exists
- New data for will be added to that partition
