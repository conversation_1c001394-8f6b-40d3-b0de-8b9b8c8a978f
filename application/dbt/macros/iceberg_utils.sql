{% macro delete_iceberg_partition(table_name, partition_column, partition_value) %}
 {#
    This macro deletes a specific partition from an Iceberg table in Athena if it exists.

    Args:
        table_name: The fully qualified table name (database.schema.table)
        partition_column: The column used for partitioning (e.g., 'crawler_run_date')
        partition_value: The value of the partition to delete (e.g., '2023-01-01')

    #}

  {% set delete_query %}
    DELETE FROM {{ table_name }}
    WHERE {{ partition_column }} = CAST('{{ partition_value }}' AS DATE)
  {% endset %}

  {% do run_query(delete_query) %}
  {% do log("Deleted partition where " ~ partition_column ~ " = '" ~ partition_value ~ "' from table " ~ table_name, info=True) %}
{% endmacro %}
