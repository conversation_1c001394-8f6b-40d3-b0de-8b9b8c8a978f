{% docs __overview__ %}

# Price Crawler Analytics - Data Models

Insurance pricing data from comparison websites transformed into analytics-ready datasets for market research and competitive intelligence.
For documentation, please refer to [here](https://docs-dbt-datalake.d.eu1.caramelspec.com/price_crawler/index.html#!/overview).


## Data Sources & Models

### Comparis (Switzerland) - Motor Insurance
| Model | Description |
|-------|-------------|
| [stg_comparis_motor_insurance_pricing_data](#!/model/model.price_crawler_analytics.stg_comparis_motor_insurance_pricing_data) | Motor insurance pricing from en.comparis.ch with detailed coverage options, premiums, deductibles, and provider ratings. Includes liability, comprehensive, and partial coverage breakdowns. |

### Check24 (Germany) - Property Insurance
| Model | Description |
|-------|-------------|
| [stg_check24_building_insurance_pricing_data](#!/model/model.price_crawler_analytics.stg_check24_building_insurance_pricing_data) | Building insurance pricing from check24.de covering structural damage protection. Includes provider comparisons, pricing, and coverage highlights. |
| [stg_check24_content_insurance_pricing_data](#!/model/model.price_crawler_analytics.stg_check24_content_insurance_pricing_data) | Content insurance pricing from check24.de for personal belongings protection. Contains pricing recommendations and provider ratings. |

## Key Features

- **Iceberg Tables**: Partitioned by `crawler_run_date` for efficient querying
- **Experiment Tracking**: Full reproducibility with experiment metadata
- **Time Series**: Historical pricing trends and market analysis

## Common Usage

```sql
-- Pricing trends over time
SELECT crawler_run_date, provider, AVG(price) as avg_price
FROM {{ '{{ ref(\'stg_check24_building_insurance_pricing_data\') }}' }}
GROUP BY crawler_run_date, provider

-- Market comparison
SELECT provider, COUNT(*) as products, AVG(price) as avg_price
FROM {{ '{{ ref(\'stg_check24_content_insurance_pricing_data\') }}' }}
WHERE crawler_run_date = '2024-01-15'
GROUP BY provider
```

{% enddocs %}
