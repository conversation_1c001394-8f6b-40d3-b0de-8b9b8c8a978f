{{
  config(
    materialized='incremental',
    incremental_strategy='insert_overwrite',
    partitioned_by=['crawler_run_date']
  )
}}

SELECT
    provider,
    tariff_name,
    price,
    recommendation,
    grade,
    experiment_filename,
    experiment_file_md5_hash,
    experiment_name,
    project_name,
    tariff,
    index,
    url,
    status,
    status_reason,
    '{{ var('iptiq_load_id', 'NULL') }}'                        AS iptiq_load_id,
    '{{ var('iptiq_load_date', 'NULL') }}'                      AS iptiq_load_date,
    '{{ var('iptiq_execution_date', 'NULL') }}'                 AS iptiq_execution_date,
    CAST(crawler_run_date AS DATE)                              AS crawler_run_date
FROM {{ source('databucket_raw', 'check24_content_insurance_pricing_data') }}
WHERE
    crawler_run_date = '{{ var('crawler_run_date', "'1900-01-01'") }}'
