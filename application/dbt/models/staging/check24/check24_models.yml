version: 2

sources:
  - name: databucket_raw
    description: Raw data from various crawlers stored in Athena
    tables:
      - name: check24_building_insurance_pricing_data
        description: '{{ doc("check24_building_insurance_pricing_data_source") }}'
        columns:
          - name: provider
            description: '{{ doc("provider") }}'
          - name: tariff_name
            description: '{{ doc("tariff_name") }}'
          - name: price
            description: '{{ doc("price") }}'
          - name: highlight
            description: '{{ doc("highlight") }}'
          - name: grade
            description: '{{ doc("grade") }}'
          - name: experiment_filename
            description: '{{ doc("experiment_filename") }}'
          - name: experiment_name
            description: '{{ doc("experiment_name") }}'
          - name: project_name
            description: '{{ doc("project_name") }}'
          - name: tariff
            description: '{{ doc("tariff") }}'
          - name: index
            description: '{{ doc("index") }}'
          - name: url
            description: '{{ doc("url") }}'
          - name: status
            description: '{{ doc("status") }}'
          - name: status_reason
            description: '{{ doc("status_reason") }}'
          - name: iptiq_load_id
            description: '{{ doc("iptiq_load_id") }}'
          - name: iptiq_load_source
            description: '{{ doc("iptiq_load_source") }}'
          - name: iptiq_load_date
            description: '{{ doc("iptiq_load_date") }}'
          - name: iptiq_execution_date
            description: '{{ doc("iptiq_execution_date") }}'
          - name: iptiq_ingestion_date
            description: '{{ doc("iptiq_ingestion_date") }}'
          - name: experiment_file_md5_hash
            description: '{{ doc("experiment_file_md5_hash") }}'
          - name: crawler_run_date
            description: '{{ doc("crawler_run_date") }}'
            tests:
              - not_null

      - name: check24_content_insurance_pricing_data
        description: '{{ doc("check24_content_insurance_pricing_data_source") }}'
        columns:
          - name: provider
            description: '{{ doc("provider") }}'
          - name: tariff_name
            description: '{{ doc("tariff_name") }}'
          - name: price
            description: '{{ doc("price") }}'
          - name: recommendation
            description: '{{ doc("recommendation") }}'
          - name: grade
            description: '{{ doc("grade") }}'
          - name: experiment_filename
            description: '{{ doc("experiment_filename") }}'
          - name: experiment_name
            description: '{{ doc("experiment_name") }}'
          - name: project_name
            description: '{{ doc("project_name") }}'
          - name: tariff
            description: '{{ doc("tariff") }}'
          - name: index
            description: '{{ doc("index") }}'
          - name: url
            description: '{{ doc("url") }}'
          - name: status
            description: '{{ doc("status") }}'
          - name: status_reason
            description: '{{ doc("status_reason") }}'
          - name: iptiq_load_id
            description: '{{ doc("iptiq_load_id") }}'
          - name: iptiq_load_source
            description: '{{ doc("iptiq_load_source") }}'
          - name: iptiq_load_date
            description: '{{ doc("iptiq_load_date") }}'
          - name: iptiq_execution_date
            description: '{{ doc("iptiq_execution_date") }}'
          - name: iptiq_ingestion_date
            description: '{{ doc("iptiq_ingestion_date") }}'
          - name: experiment_file_md5_hash
            description: '{{ doc("experiment_file_md5_hash") }}'
          - name: crawler_run_date
            description: '{{ doc("crawler_run_date") }}'
            tests:
              - not_null

models:
  - name: stg_check24_building_insurance_pricing_data
    description: '{{ doc("stg_check24_building_insurance_pricing_data") }}'
    columns:
      - name: provider
        description: '{{ doc("provider") }}'
        tests:
          - not_null
      - name: tariff_name
        description: '{{ doc("tariff_name") }}'
      - name: price
        description: '{{ doc("price") }}'
      - name: highlight
        description: '{{ doc("highlight") }}'
      - name: grade
        description: '{{ doc("grade") }}'
      - name: crawler_run_date
        description: '{{ doc("crawler_run_date") }}'
        tests:
          - not_null
      - name: iptiq_load_id
        description: '{{ doc("iptiq_load_id") }}'
      - name: iptiq_load_source
        description: '{{ doc("iptiq_load_source") }}'
      - name: iptiq_load_date
        description: '{{ doc("iptiq_load_date") }}'
      - name: iptiq_execution_date
        description: '{{ doc("iptiq_execution_date") }}'
      - name: iptiq_ingestion_date
        description: '{{ doc("iptiq_ingestion_date") }}'
      - name: experiment_file_md5_hash
        description: '{{ doc("experiment_file_md5_hash") }}'
      - name: iptiq_load_id
        description: '{{ doc("iptiq_load_id") }}'
      - name: iptiq_load_date
        description: '{{ doc("iptiq_load_date") }}'
      - name: iptiq_execution_date
        description: '{{ doc("iptiq_execution_date") }}'

  - name: stg_check24_content_insurance_pricing_data
    description: '{{ doc("stg_check24_content_insurance_pricing_data") }}'
    columns:
      - name: provider
        description: '{{ doc("provider") }}'
        tests:
          - not_null
      - name: tariff_name
        description: '{{ doc("tariff_name") }}'
      - name: price
        description: '{{ doc("price") }}'
      - name: recommendation
        description: '{{ doc("recommendation") }}'
      - name: grade
        description: '{{ doc("grade") }}'
      - name: crawler_run_date
        description: '{{ doc("crawler_run_date") }}'
        tests:
          - not_null
      - name: iptiq_load_id
        description: '{{ doc("iptiq_load_id") }}'
      - name: iptiq_load_source
        description: '{{ doc("iptiq_load_source") }}'
      - name: iptiq_load_date
        description: '{{ doc("iptiq_load_date") }}'
      - name: iptiq_execution_date
        description: '{{ doc("iptiq_execution_date") }}'
      - name: iptiq_ingestion_date
        description: '{{ doc("iptiq_ingestion_date") }}'
      - name: experiment_file_md5_hash
        description: '{{ doc("experiment_file_md5_hash") }}'
      - name: iptiq_load_id
        description: '{{ doc("iptiq_load_id") }}'
      - name: iptiq_load_date
        description: '{{ doc("iptiq_load_date") }}'
      - name: iptiq_execution_date
        description: '{{ doc("iptiq_execution_date") }}'
