{% docs check24_building_insurance_pricing_data_source %}
Raw Check24 building insurance pricing data collected through web scraping. This table contains detailed information about building insurance offerings from various providers as listed on https://wohngebaeude.check24.de/whg/vergleichsergebnis/, including pricing, coverage details, and provider information. Building insurance typically covers damage to the structure of buildings from events like fire, water damage, and natural disasters.
{% enddocs %}

{% docs check24_content_insurance_pricing_data_source %}
Raw Check24 content insurance pricing data collected through web scraping. This table contains detailed information about content insurance offerings from various providers as listed on https://hausratversicherungen.check24.de/hausrat/vergleichsergebnis/, including pricing, coverage details, and provider information. Content insurance typically covers personal belongings and household contents against theft, damage, and loss.
{% enddocs %}


{% docs highlight %}
Special highlights or features of the insurance product that are emphasized in the marketing materials, representing key selling points or differentiators. Examples include "Preis-Leistungs-Empfehlung" (price-performance recommendation) which indicates products that offer good value for money according to Check24's assessment criteria.
{% enddocs %}

{% docs grade %}
Rating or grade of the insurance product, typically represented as a numerical score (e.g., "1.0", "2.8", "4.0") that indicates the overall quality or value assessment by Check24. Lower numbers generally indicate better ratings, with 1.0 being the best possible score. The format often uses a comma as the decimal separator (German notation).
{% enddocs %}


{% docs project_name %}
Name of the project under which the crawling was performed.
{% enddocs %}

{% docs tariff %}
A combination of company/provider name and the product they offering.
{% enddocs %}

{% docs index %}
Index is the basket ID used from experiment file for this request.
{% enddocs %}

{% docs url %}
URL of the insurance product page on the Check24 website, providing a reference to the original source of the data.
{% enddocs %}

{% docs status_reason %}
Reason for the status, providing additional context about why a crawling operation succeeded or failed.
{% enddocs %}

{% docs recommendation %}
Recommendation status indicating whether the product is recommended by Check24 based on their assessment criteria. Examples include "Preis-Leistungs-" (price-performance), "Leistungs-" (performance), and "Günstigster Tarif" (cheapest tariff). Some products may have "Nothing for this product" indicating no specific recommendation. These recommendations help users identify products that excel in particular aspects.
{% enddocs %}

{% docs stg_check24_building_insurance_pricing_data %}
Staged and transformed Check24 building insurance pricing data.
{% enddocs %}

{% docs stg_check24_content_insurance_pricing_data %}
Staged and transformed Check24 content insurance pricing data.
{% enddocs %}

{% docs price %}
Insurance premium price, representing the cost of the insurance policy. This is typically shown as a monetary amount in the local currency (EUR for Check24) and may represent monthly, quarterly, or annual payment depending on the context. This is the primary measure for price comparison analysis.
{% enddocs %}

{% docs provider %}
Insurance provider name, representing the company offering the insurance product. This identifies the underwriter of the policy and is used to track offerings across different insurance companies.
{% enddocs %}

{% docs tariff_name %}
Name of the insurance tariff or product, representing the specific insurance offering with its unique set of features, coverage limits, and pricing structure. Different tariffs from the same provider may target different customer segments or offer varying levels of coverage.
{% enddocs %}
