version: 2

sources:
  - name: databucket_raw
    description: Raw data from various crawlers stored in Athena
    tables:
      - name: comparis_motor_insurance_pricing_data
        description: '{{ doc("comparis_motor_insurance_pricing_data_source") }}'
        columns:
          - name: crawler_id
            description: '{{ doc("crawler_id") }}'
            tests:
              - not_null
          - name: status
            description: '{{ doc("status") }}'
          - name: error
            description: '{{ doc("error") }}'
          - name: results
            description: '{{ doc("results") }}'
          - name: input_request
            description: '{{ doc("input_request") }}'
          - name: retry_attempts
            description: '{{ doc("retry_attempts") }}'
          - name: experiment_filename
            description: '{{ doc("experiment_filename") }}'
          - name: experiment_name
            description: '{{ doc("experiment_name") }}'
          - name: experiment_file_md5_hash
            description: '{{ doc("experiment_file_md5_hash") }}'
          - name: iptiq_load_id
            description: '{{ doc("iptiq_load_id") }}'
          - name: iptiq_load_source
            description: '{{ doc("iptiq_load_source") }}'
          - name: iptiq_load_date
            description: '{{ doc("iptiq_load_date") }}'
          - name: iptiq_execution_date
            description: '{{ doc("iptiq_execution_date") }}'
          - name: iptiq_ingestion_date
            description: '{{ doc("iptiq_ingestion_date") }}'
          - name: crawler_run_date
            description: '{{ doc("crawler_run_date") }}'
            tests:
              - not_null

models:
  - name: stg_comparis_motor_insurance_pricing_data
    description: '{{ doc("stg_comparis_motor_insurance_pricing_data") }}'
    columns:
      - name: crawler_id
        description: '{{ doc("crawler_id") }}'
        tests:
          - not_null
      - name: experiment_name
        description: '{{ doc("experiment_name") }}'
      - name: experiment_file_md5_hash
        description: '{{ doc("experiment_file_md5_hash") }}'
      - name: status
        description: '{{ doc("status") }}'
      - name: error
        description: '{{ doc("error") }}'
      - name: retry_attempts
        description: '{{ doc("retry_attempts") }}'
      - name: result_item_detail_json
        description: '{{ doc("result_item_detail_json") }}'
      - name: product_id
        description: '{{ doc("product_id") }}'
      - name: provider_id
        description: '{{ doc("provider_id") }}'
      - name: provider_name_infobase
        description: '{{ doc("provider_name_infobase") }}'
      - name: product_name_infobase
        description: '{{ doc("product_name_infobase") }}'
      - name: logo_name
        description: '{{ doc("logo_name") }}'
      - name: comparis_satisfaction_grade
        description: '{{ doc("comparis_satisfaction_grade") }}'
      - name: comparis_award
        description: '{{ doc("comparis_award") }}'
      - name: is_winner
        description: '{{ doc("is_winner") }}'
      - name: offer_quote
        description: '{{ doc("offer_quote") }}'
      - name: offer_button_type
        description: '{{ doc("offer_button_type") }}'
      - name: footnote_index_value
        description: '{{ doc("footnote_index_value") }}'
      - name: special_offer_infobase
        description: '{{ doc("special_offer_infobase") }}'
      - name: premium_first_year
        description: '{{ doc("premium_first_year") }}'
      - name: premium_first_year_discount
        description: '{{ doc("premium_first_year_discount") }}'
      - name: footnotes_json
        description: '{{ doc("footnotes_json") }}'
      - name: bonus_protection_coverage_mark
        description: '{{ doc("bonus_protection_coverage_mark") }}'
      - name: parking_damages_coverage_mark
        description: '{{ doc("parking_damages_coverage_mark") }}'
      - name: gross_negligence_coverage_mark
        description: '{{ doc("gross_negligence_coverage_mark") }}'
      - name: assistance_coverage_mark
        description: '{{ doc("assistance_coverage_mark") }}'
      - name: retention_coverage_mark
        description: '{{ doc("retention_coverage_mark") }}'
      - name: personal_effects_coverage_mark
        description: '{{ doc("personal_effects_coverage_mark") }}'
      - name: occupant_protection_coverage_mark
        description: '{{ doc("occupant_protection_coverage_mark") }}'
      - name: ecommerce_item_model_json
        description: '{{ doc("ecommerce_item_model_json") }}'
      - name: result_category
        description: '{{ doc("result_category") }}'
      - name: is_direct_buy_available
        description: '{{ doc("is_direct_buy_available") }}'
      - name: has_mileage_pricing
        description: '{{ doc("has_mileage_pricing") }}'
      - name: is_top_box_ad
        description: '{{ doc("is_top_box_ad") }}'
      - name: benefits_json
        description: '{{ doc("benefits_json") }}'
      - name: liability_cover_premium
        description: '{{ doc("liability_cover_premium") }}'
      - name: liability_cover_deductible_young_driver
        description: '{{ doc("liability_cover_deductible_young_driver") }}'
      - name: liability_cover_young_driver_infobase_key
        description: '{{ doc("liability_cover_young_driver_infobase_key") }}'
      - name: liability_cover_deductible_other_driver
        description: '{{ doc("liability_cover_deductible_other_driver") }}'
      - name: has_liability_bonus_cover
        description: '{{ doc("has_liability_bonus_cover") }}'
      - name: has_comprehensive_bonus_cover
        description: '{{ doc("has_comprehensive_bonus_cover") }}'
      - name: has_partial_cover
        description: '{{ doc("has_partial_cover") }}'
      - name: partial_cover_premium
        description: '{{ doc("partial_cover_premium") }}'
      - name: partial_cover_deductible
        description: '{{ doc("partial_cover_deductible") }}'
      - name: requested_partial_cover_deductible
        description: '{{ doc("requested_partial_cover_deductible") }}'
      - name: is_requested_partial_cover_deductible
        description: '{{ doc("is_requested_partial_cover_deductible") }}'
      - name: has_comprehensive_cover
        description: '{{ doc("has_comprehensive_cover") }}'
      - name: comprehensive_cover_premium
        description: '{{ doc("comprehensive_cover_premium") }}'
      - name: comprehensive_cover_deductible_young_driver
        description: '{{ doc("comprehensive_cover_deductible_young_driver") }}'
      - name: comprehensive_cover_young_driver_infobase_key
        description: '{{ doc("comprehensive_cover_young_driver_infobase_key") }}'
      - name: comprehensive_cover_deductible_other_driver
        description: '{{ doc("comprehensive_cover_deductible_other_driver") }}'
      - name: is_requested_comprehensive_cover_deductible
        description: '{{ doc("is_requested_comprehensive_cover_deductible") }}'
      - name: requested_comprehensive_cover_deductible
        description: '{{ doc("requested_comprehensive_cover_deductible") }}'
      - name: has_personal_effects
        description: '{{ doc("has_personal_effects") }}'
      - name: has_parking_damage
        description: '{{ doc("has_parking_damage") }}'
      - name: has_parking_damage_limited
        description: '{{ doc("has_parking_damage_limited") }}'
      - name: has_gross_negligence
        description: '{{ doc("has_gross_negligence") }}'
      - name: has_assistance
        description: '{{ doc("has_assistance") }}'
      - name: has_occupant_protection
        description: '{{ doc("has_occupant_protection") }}'
      - name: occupant_protection_premium
        description: '{{ doc("occupant_protection_premium") }}'
      - name: satutory_charges
        description: '{{ doc("satutory_charges") }}'
      - name: is_finished
        description: '{{ doc("is_finished") }}'
      - name: crawler_run_date
        description: '{{ doc("crawler_run_date") }}'
        tests:
          - not_null
      - name: iptiq_load_id
        description: '{{ doc("iptiq_load_id") }}'
      - name: iptiq_load_date
        description: '{{ doc("iptiq_load_date") }}'
      - name: iptiq_execution_date
        description: '{{ doc("iptiq_execution_date") }}'
