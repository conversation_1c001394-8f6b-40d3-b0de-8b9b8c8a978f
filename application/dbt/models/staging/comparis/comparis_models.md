{% docs comparis_motor_insurance_pricing_data_source %}
Raw Comparis motor insurance pricing data collected through web scraping. This table contains detailed information about motor insurance offerings from various providers as listed on https://en.comparis.ch/autoversicherung, including pricing, coverage details, and provider information.
{% enddocs %}

{% docs crawler_id %}
Unique identifier for the crawler run that collected the data. Used to track and identify specific data collection sessions.
{% enddocs %}

{% docs error %}
Error message if the crawling operation failed, providing details about what went wrong during data collection.
{% enddocs %}

{% docs results %}
JSON string containing the complete crawling results, including all data points collected from the website.
{% enddocs %}

{% docs input_request %}
Original request parameters used to initiate the crawling operation, including search criteria and configuration.
{% enddocs %}

{% docs retry_attempts %}
Number of retry attempts made during the crawling operation if initial attempts failed.
{% enddocs %}

{% docs result_item_detail_json %}
Detailed JSON string containing specific information about each insurance product item, including coverage details, premiums, and other product-specific attributes.
{% enddocs %}

{% docs stg_comparis_motor_insurance_pricing_data %}
Staged and transformed Comparis motor insurance pricing data. This model extracts and structures the relevant information from the raw JSON data, making it easier to analyze and compare insurance offerings.
{% enddocs %}

{% docs product_id %}
Unique product identifier assigned by Comparis to each insurance product offering.
{% enddocs %}

{% docs provider_id %}
Insurance provider identifier assigned by Comparis to each insurance company.
{% enddocs %}

{% docs provider_name_infobase %}
Provider name as stored in the Comparis infobase, representing the official name of the insurance company.
{% enddocs %}

{% docs product_name_infobase %}
Product name as stored in the Comparis infobase, representing the official name of the insurance product.
{% enddocs %}

{% docs logo_name %}
Filename or identifier for the insurance provider's logo as displayed on Comparis.
{% enddocs %}

{% docs comparis_satisfaction_grade %}
Customer satisfaction grade assigned by Comparis, typically a numerical score indicating customer satisfaction with the provider.
{% enddocs %}

{% docs comparis_award %}
Award category or recognition given by Comparis to the insurance product, if any.
{% enddocs %}

{% docs is_winner %}
Boolean flag indicating whether the insurance product was marked as a winner or top recommendation in its category.
{% enddocs %}

{% docs offer_quote %}
Quotation or specific offer details provided for the insurance product.
{% enddocs %}

{% docs offer_button_type %}
Type of offer button displayed on the Comparis website for this product.
{% enddocs %}

{% docs footnote_index_value %}
Index value for footnotes associated with the insurance product listing.
{% enddocs %}

{% docs special_offer_infobase %}
Special offer information as stored in the Comparis infobase, detailing any promotions or special deals.
{% enddocs %}

{% docs premium_first_year %}
Premium amount for the first year of the insurance policy, representing the initial cost to the customer.
{% enddocs %}

{% docs premium_first_year_discount %}
Discount amount applied to the first year premium, if any.
{% enddocs %}

{% docs footnotes_json %}
JSON string containing footnotes associated with the insurance product listing.
{% enddocs %}

{% docs bonus_protection_coverage_mark %}
Indicator for bonus protection coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs parking_damages_coverage_mark %}
Indicator for parking damages coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs gross_negligence_coverage_mark %}
Indicator for gross negligence coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs assistance_coverage_mark %}
Indicator for assistance coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs retention_coverage_mark %}
Indicator for retention coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs personal_effects_coverage_mark %}
Indicator for personal effects coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs occupant_protection_coverage_mark %}
Indicator for occupant protection coverage, typically a numerical value or code representing the level of coverage.
{% enddocs %}

{% docs ecommerce_item_model_json %}
JSON string containing e-commerce item model data used for tracking and analytics on the Comparis website.
{% enddocs %}

{% docs result_category %}
Category of the result as classified by Comparis, typically a numerical value representing the product tier or category.
{% enddocs %}

{% docs is_direct_buy_available %}
Boolean flag indicating whether the insurance product can be purchased directly through the Comparis platform.
{% enddocs %}

{% docs has_mileage_pricing %}
Boolean flag indicating whether the insurance product offers mileage-based pricing options.
{% enddocs %}

{% docs is_top_box_ad %}
Boolean flag indicating whether the insurance product was displayed as a premium or featured listing.
{% enddocs %}

{% docs benefits_json %}
JSON string containing detailed information about the benefits offered by the insurance product.
{% enddocs %}

{% docs liability_cover_premium %}
Premium amount specifically for the liability coverage component of the insurance.
{% enddocs %}

{% docs liability_cover_deductible_young_driver %}
Deductible amount for liability coverage applicable to young drivers.
{% enddocs %}

{% docs liability_cover_young_driver_infobase_key %}
Infobase key for young driver liability coverage information, used for reference in the Comparis system.
{% enddocs %}

{% docs liability_cover_deductible_other_driver %}
Deductible amount for liability coverage applicable to drivers who are not classified as young drivers.
{% enddocs %}

{% docs has_liability_bonus_cover %}
Boolean flag indicating whether the insurance product includes liability bonus protection coverage.
{% enddocs %}

{% docs has_comprehensive_bonus_cover %}
Boolean flag indicating whether the insurance product includes comprehensive bonus protection coverage.
{% enddocs %}

{% docs has_partial_cover %}
Boolean flag indicating whether the insurance product includes partial coverage options.
{% enddocs %}

{% docs partial_cover_premium %}
Premium amount specifically for the partial coverage component of the insurance.
{% enddocs %}

{% docs partial_cover_deductible %}
Deductible amount for partial coverage.
{% enddocs %}

{% docs requested_partial_cover_deductible %}
Deductible amount for partial coverage as requested by the customer during the quote process.
{% enddocs %}

{% docs is_requested_partial_cover_deductible %}
Boolean flag indicating whether the partial cover deductible was specifically requested by the customer.
{% enddocs %}

{% docs has_comprehensive_cover %}
Boolean flag indicating whether the insurance product includes comprehensive coverage options.
{% enddocs %}

{% docs comprehensive_cover_premium %}
Premium amount specifically for the comprehensive coverage component of the insurance.
{% enddocs %}

{% docs comprehensive_cover_deductible_young_driver %}
Deductible amount for comprehensive coverage applicable to young drivers.
{% enddocs %}

{% docs comprehensive_cover_young_driver_infobase_key %}
Infobase key for young driver comprehensive coverage information, used for reference in the Comparis system.
{% enddocs %}

{% docs comprehensive_cover_deductible_other_driver %}
Deductible amount for comprehensive coverage applicable to drivers who are not classified as young drivers.
{% enddocs %}

{% docs is_requested_comprehensive_cover_deductible %}
Boolean flag indicating whether the comprehensive cover deductible was specifically requested by the customer.
{% enddocs %}

{% docs requested_comprehensive_cover_deductible %}
Deductible amount for comprehensive coverage as requested by the customer during the quote process.
{% enddocs %}

{% docs has_personal_effects %}
Boolean flag indicating whether the insurance product includes coverage for personal effects.
{% enddocs %}

{% docs has_parking_damage %}
Boolean flag indicating whether the insurance product includes coverage for parking damages.
{% enddocs %}

{% docs has_parking_damage_limited %}
Boolean flag indicating whether the insurance product includes limited coverage for parking damages.
{% enddocs %}

{% docs has_gross_negligence %}
Boolean flag indicating whether the insurance product includes coverage for gross negligence.
{% enddocs %}

{% docs has_assistance %}
Boolean flag indicating whether the insurance product includes assistance services.
{% enddocs %}

{% docs has_occupant_protection %}
Boolean flag indicating whether the insurance product includes occupant protection coverage.
{% enddocs %}

{% docs occupant_protection_premium %}
Premium amount specifically for the occupant protection component of the insurance.
{% enddocs %}

{% docs satutory_charges %}
Statutory charges applied to the insurance premium, such as taxes or mandatory fees.
{% enddocs %}

{% docs is_finished %}
Boolean flag indicating whether the data collection and processing for this record is complete.
{% enddocs %}
