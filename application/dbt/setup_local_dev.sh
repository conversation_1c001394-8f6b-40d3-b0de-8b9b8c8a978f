#!/bin/bash

# Price Crawler dbt Local Development Setup Script
# This script helps set up the dbt environment for local development

set -e  # Exit on any error

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check the Python version
check_python_version() {
    echo "[INFO] Checking Python version..."

    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
        PYTHON_MAJOR=$(echo "$PYTHON_VERSION" | cut -d'.' -f1)
        PYTHON_MINOR=$(echo "$PYTHON_VERSION" | cut -d'.' -f2)

        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 10 ]; then
            echo "[SUCCESS] Python $PYTHON_VERSION found"
            return 0
        else
            echo "[ERROR] Python 3.10+ required, found $PYTHON_VERSION"
            return 1
        fi
    else
        echo "[ERROR] Python 3 not found. Please install Python 3.10+"
        return 1
    fi
}

# Function to check the AWS CLI
check_aws_cli() {
    echo "[INFO] Checking AWS CLI..."

    if command_exists aws; then
        AWS_VERSION=$(aws --version 2>&1 | cut -d' ' -f1 | cut -d'/' -f2)
        echo "[SUCCESS] AWS CLI $AWS_VERSION found"

        # Test AWS credentials
        if aws sts get-caller-identity >/dev/null 2>&1; then
            echo "[SUCCESS] AWS credentials configured"
            return 0
        else
            echo "[WARNING] AWS credentials not configured or invalid"
            echo "[INFO] Please run 'aws configure' to set up your credentials"
            return 1
        fi
    else
        echo "[ERROR] AWS CLI not found. Please install AWS CLI"
        return 1
    fi
}

# Function to set up the virtual environment
setup_virtual_env() {
    echo "[INFO] Setting up Python virtual environment..."

    if [ ! -d "dbt_env" ]; then
        python3 -m venv dbt_env
        echo "[SUCCESS] Virtual environment created"
    else
        echo "[WARNING] Virtual environment already exists"
    fi

    # Activate virtual environment
    # shellcheck disable=SC1091
    source dbt_env/bin/activate

    # Upgrade pip
    pip install --upgrade pip
    echo "[SUCCESS] Virtual environment activated and pip upgraded"
}

# Function to install dependencies
install_dependencies() {
    echo "[INFO] Installing dbt dependencies..."

    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        echo "[SUCCESS] Dependencies installed from requirements.txt"
    else
        echo "[ERROR] requirements.txt not found"
        return 1
    fi

    # Verify dbt installation
    if command_exists dbt; then
        DBT_VERSION=$(dbt --version | head -n2)
        echo "[SUCCESS] dbt installed: $DBT_VERSION"
    else
        echo "[ERROR] dbt installation failed"
        return 1
    fi
}

# Function to install dbt packages
install_dbt_packages() {
    echo "[INFO] Installing dbt packages..."

    if [ -f "packages.yml" ]; then
        dbt deps
        echo "[SUCCESS] dbt packages installed"
    else
        echo "[ERROR] packages.yml not found"
        return 1
    fi
}

# Function to set up environment variables
setup_environment_variables() {
    echo "[INFO] Setting up environment variables..."

    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        cat > .env << 'EOF'
# dbt Environment Variables for Price Crawler
# Copy this file and update with your specific values

# Required AWS/Athena Configuration
export DBT_S3_STAGING_DIR=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/athena_query_results/dbt_workgroup/price_crawler/dbt_temp
export DBT_S3_DATA_DIR=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/target/price_crawler/dbt
export DBT_ATHENA_WORKGROUP=dbt_workgroup
export DBT_S3_ACL=bucket-owner-full-control
export DBT_S3_SSEKMSID='acb2aaab-65c0-4b73-866a-457796a8fc34'
export DBT_S3_SERVER_SIDE_ENCRYPTION=aws:kms

# Personal Configuration (UPDATE THIS)
export USER=your_username_here

# Optional Configuration
export AWS_REGION=eu-central-1
export DBT_ATHENA_DATABASE=awsdatacatalog
export DBT_ATHENA_SCHEMA=price_crawler

# For personal development schema
# export DBT_ATHENA_SCHEMA=$USER
EOF
        echo "[SUCCESS] Created .env file template"
        echo "[WARNING] Please update the USER variable in .env file with your username"
    else
        echo "[WARNING] .env file already exists"
    fi

    # Prompt user to update USER variable
    read -rp "Enter your username for personal schema (or press Enter to skip): " username
    if [ -n "$username" ]; then
        sed -i.bak "s/your_username_here/$username/" .env
        echo "[SUCCESS] Updated USER variable to: $username"
    fi
}

# Function to test the dbt connection
test_dbt_connection() {
    echo "[INFO] Testing dbt connection..."

    # Source environment variables
    if [ -f ".env" ]; then
        # shellcheck disable=SC1091  # We checked it exists
        source .env
    fi

    # Run dbt debug
    if dbt debug; then
        echo "[SUCCESS] dbt connection test passed"
        return 0
    else
        echo "[ERROR] dbt connection test failed"
        echo "[INFO] Please check your AWS credentials and environment variables"
        return 1
    fi
}

# Function to create helpful scripts
create_helper_scripts() {
    echo "[INFO] Creating helper scripts..."

    # Create activation script
    cat > activate_dbt.sh << 'EOF'
#!/bin/bash
# Activate dbt environment and load environment variables

if [ -f "dbt_env/bin/activate" ]; then
    source dbt_env/bin/activate
else
    echo "Error: Virtual environment 'dbt_env' not found. Create it first with 'python3 -m venv dbt_env'." >&2
    exit 1
fi
if [ -f ".env" ]; then
    source .env
    echo "dbt environment activated with environment variables loaded"
else
    echo "dbt environment activated (no .env file found)"
fi

echo "dbt version: $(dbt --version | head -n2)"
EOF

    chmod +x activate_dbt.sh
    echo "[SUCCESS] Created activate_dbt.sh script"

    # Create quick test script
    cat > quick_test.sh << 'EOF'
#!/bin/bash
# Quick test script for dbt setup

source dbt_env/bin/activate
if [ -f ".env" ]; then
    source .env
fi

echo "Running dbt debug..."
dbt debug

echo "Running dbt compile..."
dbt compile

echo "Setup test completed!"
EOF

    chmod +x quick_test.sh
    echo "[SUCCESS] Created quick_test.sh script"
}

# Main setup function
main() {
    echo "========================================"
    echo "Price Crawler dbt Local Setup"
    echo "========================================"

    # Check if we're in the right directory
    if [ ! -f "dbt_project.yml" ]; then
        echo "[ERROR] dbt_project.yml not found. Please run this script from the application/dbt directory"
        exit 1
    fi

    # Run setup steps
    check_python_version || exit 1
    check_aws_cli || echo "[WARNING] AWS CLI issues detected - you may need to configure credentials"

    setup_virtual_env || exit 1
    install_dependencies || exit 1
    install_dbt_packages || exit 1
    setup_environment_variables
    create_helper_scripts

    echo "[INFO] Testing dbt connection..."
    if test_dbt_connection; then
       cat >&2 <<EOF
        "========================================"
        "Setup completed successfully!"
        "========================================"
        "Next steps:"
        "1. Activate the environment: source activate_dbt.sh"
        "2. Run a quick test: ./quick_test.sh"
        "3. Try running a model: dbt run --select stg_comparis_motor_insurance_pricing_data --profile personal_schema"
        "4. Generate docs:  dbt docs generate --profile personal_schema  && dbt docs serve  --profile personal_schema"
EOF
    else
      cat >&2 <<EOF
        "========================================"
        "Setup completed with warnings"
        "========================================"
        "Please fix the connection issues and run:"
        "source activate_dbt.sh && dbt debug"
EOF
    fi
}

# Run main function
main "$@"
