price_crawler_analytics:
  target: price_crawler
  outputs:
    price_crawler:
      type: athena
      s3_staging_dir: "{{ env_var('DBT_S3_STAGING_DIR') | as_text }}"
      s3_data_dir: "{{ env_var('DBT_S3_DATA_DIR') | as_text }}"
      s3_data_naming: "{{ env_var('DBT_S3_DATA_NAMING', 'schema_table') }}"
      region_name: "{{ env_var('AWS_REGION', 'eu-central-1') }}"
      database: "{{ env_var('DBT_ATHENA_DATABASE', 'awsdatacatalog') }}"
      schema: "{{ env_var('DBT_ATHENA_SCHEMA', 'price_crawler') }}"
      work_group: "{{ env_var('DBT_ATHENA_WORKGROUP', 'dbt_workgroup') }}"
      num_retries: 1
      poll_interval: 1
      threads: 4
      seed_s3_upload_args:
        ACL: "{{ env_var('DBT_S3_ACL','bucket-owner-full-control') | as_text }}"
        SSEKMSKeyId: "{{ env_var('DBT_S3_SSEKMSID', '') | as_text }}"
        ServerSideEncryption: "{{ env_var('DBT_S3_SERVER_SIDE_ENCRYPTION','aws:kms') | as_text }}"

personal_schema:
  target: personal
  outputs:
    personal:
      type: athena
      s3_staging_dir: "{{ env_var('DBT_S3_STAGING_DIR') | as_text }}"
      s3_data_dir: "{{ env_var('DBT_S3_DATA_DIR') | as_text }}"
      s3_data_naming: "{{ env_var('DBT_S3_DATA_NAMING', 'schema_table') }}"
      region_name: "{{ env_var('AWS_REGION', 'eu-central-1') }}"
      schema: "{{ env_var('USER') | as_text }}"
      database: "{{ env_var('DBT_ATHENA_DATABASE', 'awsdatacatalog') }}"
      work_group: "{{ env_var('DBT_ATHENA_WORKGROUP', 'dbt_workgroup') }}"
      num_retries: 1
      threads: 1
      seed_s3_upload_args:
        ACL: "{{ env_var('DBT_S3_ACL','bucket-owner-full-control') | as_text }}"
        SSEKMSKeyId: "{{ env_var('DBT_S3_SSEKMSID') | as_text }}"
        ServerSideEncryption: "{{ env_var('DBT_S3_SERVER_SIDE_ENCRYPTION') | as_text }}"
