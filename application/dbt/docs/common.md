{% docs crawler_run_date %}
Date when the crawler was executed. This field is used for partitioning data in the datalake and tracking historical pricing information over time. It represents the point in time when the data was collected from the source website and is crucial for time-series analysis of insurance pricing trends.
{% enddocs %}

{% docs experiment_filename %}
Name of the experiment file that defined the parameters and configuration for the crawling operation, used for tracking and reproducibility.
{% enddocs %}

{% docs experiment_name %}
Name of the experiment that was conducted, providing context about the purpose and scope of the data collection effort.
{% enddocs %}

{% docs status %}
Status of the crawling operation, indicating whether it was successful or failed. Possible values include 'success' or 'Failed'.
{% enddocs %}

{% docs iptiq_load_id %}
Unique identifier assigned by the Airflow DAG to the data load process.
{% enddocs %}

{% docs iptiq_load_source %}
Source system or process that generated the data load into the Datalake, providing context about the origin of the data.
{% enddocs %}

{% docs iptiq_load_date %}
Date assigned by the Airflow DAG referring to when the data was loaded.
{% enddocs %}

{% docs iptiq_execution_date %}
Execution date of the Airflow DAG.
{% enddocs %}

{% docs iptiq_ingestion_date %}
Data Ingestion date based on the Airflow DAG run context.
{% enddocs %}

{% docs experiment_file_md5_hash %}
MD5 hash of the experiment file, providing a unique fingerprint to verify file integrity and detect changes in the experiment configuration.
{% enddocs %}
