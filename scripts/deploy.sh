#!/usr/bin/env bash

# Deploys helm charts for components of airflow cluster
set -euo pipefail

scripts_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"

# shellcheck source=./common.sh
source "${scripts_dir}/common.sh"

function usage() {
  echo -n "
Deploy the project components for datalake
usage: ./deploy.sh <command>

commands:
  * airflow-cloud: deploy datalake project components artifacts to Airflow running on EKS cluster
  * airflow-local: deploy datalake project components artifacts to Airflow running on local cluster
"
}

function _deploy_airflow_cloud() {
  local target_ns
  target_ns="${AIRFLOW_NAMESPACE:-airflow}"

  [[ "$(get_ecr_uri datalake-airflow)" =~ ([^/]*)/(.*) ]] && registry_host=${BASH_REMATCH[1]} && repository=${BASH_REMATCH[2]}

  echo "===> fetching helm chart for airflow-deploy"
  fetch_helm_chart airflow-deploy "$registry_host" "$repository"

  echo "===> deploying ${project_name} artifacts to Airflow"
  upgrade_helm_chart "datalake-${project_name}-airflow-deploy" "${helm_dir}/charts/airflow-deploy" "${target_ns}" \
    --set airflow.s3SyncPostInstallHook.s3_location="$DAGS_BUCKET" \
    --set connections.bright_data_webscraper.password="$BRIGHT_DATE_WEBSCRAPER_PWD" \
    --set-string connections.bright_data_webscraper.extra.ssl_certificate="${BRIGHT_DATE_SSL_CERTIFICATE}" \
    --timeout=900s
}

function _deploy_airflow_local() {
  pipeline_version="${PIPELINE_VERSION:-$(get_version)}"
  local target_ns
  target_ns="${AIRFLOW_NAMESPACE:-default}"

  ENVIRONMENT_SHORT=local
  PYTHON_BIN=3.10.12

  echo "===> Packaged dags"
  cd "${application_dir}/airflow/dags/"
  if [ "$SYNC_ONLY_DAGS" == true ]; then
    zip -r9 "${LOCAL_DAGS_PATH}/zip_dags_${project_name}" . -x '*__pycache__*' -x '*.pytest_cache*' -x '*.DS_Store'
  else

    rm -f "${LOCAL_DAGS_PATH}/zip_dags_${project_name}.zip"

    echo "===> deploying ${project_name} artifacts to Airflow"
    upgrade_helm_chart "datalake-${project_name}-airflow-deploy" "$AIRFLOW_DEPLOY_HELM_CHART_PATH" "$target_ns"

    echo "===> constructing and transferring packaged dag zip"
    # create a local temporary packaged dag zip
    zip -r9 "zip_dags_${project_name}" . -x '*__pycache__*' -x '*.pytest_cache*' -x '*.DS_Store'

    # add quantum-data-pipeline inside packaged dag
    python3 -m pip install virtualenv==20.17.1
    python3 -m virtualenv --python=$PYTHON_BIN isolated-env
    # shellcheck disable=SC1091
    source isolated-env/bin/activate

    # Check if the Python version matches the desired version
    if [[ $(python --version 2>&1) == *$PYTHON_BIN* ]]; then
        echo "Python version $PYTHON_BIN is correct."
    else
        echo "Python version $PYTHON_BIN is not supported. Aborting."
        exit 1
    fi


    pip install --no-deps quantum-data-pipeline=="${pipeline_version}" --extra-index-url https://__token__:"${GITLAB_TOKEN}"@gitlab.com/api/v4/projects/23534375/packages/pypi/simple -t ./isolated-env/ #gitleaks:allow
    cd isolated-env/
    zip -r9 "../zip_dags_${project_name}" quantum_data_pipeline -x '*__pycache__*' -x '*.pytest_cache*' -x '*.DS_Store' && cd ..
    deactivate
    rm -r isolated-env

    # move the temporary packaged dag zip to the pv
    mv "zip_dags_${project_name}.zip" "${LOCAL_DAGS_PATH}/"
  fi

  echo "===> Copy dbt"
  cd "${application_dir}/dbt/"
  rsync -ar . "${LOCAL_DAGS_PATH}/${project_name}_dbt" --exclude={target,dbt_packages,logs,Dockerfile,README.md,.user.yml,.gitignore,dbt_env}

}

function _teardown_local() {
  local target_ns
  target_ns="${AIRFLOW_NAMESPACE:-default}"

  if helm status "datalake-${project_name}-airflow-deploy" -n "$target_ns" &>/dev/null; then
    echo "===> un-deploying ${project_name} artifacts from Airflow"
    helm delete "datalake-${project_name}-airflow-deploy" -n "$target_ns"
  fi

  echo "===> un-deploying ${project_name} artifacts from localstack"
  export AWS_ACCESS_KEY_ID=foo
  export AWS_SECRET_ACCESS_KEY=bar
  rm -f "${LOCAL_DAGS_PATH}/zip_dags_${project_name}.zip"
  rm -rf "${LOCAL_DAGS_PATH}/${project_name}_dbt/"
}

case "${1:-}" in
airflow-cloud)
  ensure_executables helm
  ensure_variables DAGS_BUCKET
  _deploy_airflow_cloud
  ;;
airflow-local)
  ensure_executables helm
  ensure_variables AIRFLOW_DEPLOY_HELM_CHART_PATH
  _deploy_airflow_local
  ;;
teardown-local) _teardown_local ;;
*)
  usage
  exit 1
  ;;
esac
