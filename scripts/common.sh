#!/usr/bin/env bash

##########################
# Common utility functions
##########################

function get_version() {
  local version

  if [ -f "$version_file" ]; then
    version=$(cat "$version_file")
  fi

  echo "$version"
}

# shellcheck disable=SC2120
function ensure_executables() {
  executables=("$@")

  for c in "${executables[@]}"; do
    hash "$c" 2>/dev/null || { echo "Required executable $c not available"; exit 1; }
  done
}

function ensure_variables() {
  vars=("$@")

  # shellcheck disable=SC2207
  IFS=$'\n' sorted_unique_vars=($(sort -u <<< "${vars[*]}")); unset IFS
  for v in "${sorted_unique_vars[@]}"; do
    [ -n "${!v:-}" ] || { echo "Required environment variable '$v' undefined."; exit 1; }
  done
}

function publish_ecr_image() {
  if [ $# -lt 3 ]; then
    echo "Usage: publish_ecr_image <image_name> <image_tag> <registry_host> <repository>"
    exit 1
  fi

  local image_name image_tag registry_host repository
  image_name="$1"; shift
  image_tag="$1"; shift
  registry_host="$1"; shift
  repository="$1"

  target="${registry_host}/${repository}:${image_tag}"

  login_ecr "$registry_host"
  docker tag "${image_name}:${image_tag}" "$target"
  printf "==> publishing image %s\n" "$target"
  docker push "$target"
  echo "===> publish complete"
}

function login_ecr() {
  if [ $# -lt 1 ]; then
    echo "Usage: login_ecr <registry_host>"
    exit 1
  fi

  local registry_host
  registry_host="$1"

  aws ecr get-authorization-token --output text --query 'authorizationData[].authorizationToken' | base64 -d | cut -d: -f2 | docker login --username AWS --password-stdin "${registry_host}"
}

function get_ecr_uri() {
  if [ $# -ne 1 ]; then
    echo "Usage: get_ecr_uri <repo_name>"
    exit 1
  fi
  local repo_name
  repo_name="$1"

  uri=$(aws ecr describe-repositories --repository-name datalake-airflow | jq -r '.repositories[0].repositoryUri')
  echo "$uri"
}

function get_latest_ecr_image_tag() {
  if [ $# -ne 1 ]; then
    echo "Usage: get_latest_ecr_image_tag <repo_name>"
    exit 1
  fi

  local repo_name
  repo_name="$1"
  latest_image_tag=$(aws ecr describe-images --query 'sort_by(imageDetails,& imagePushedAt)[*].imageTags[0]' --repository-name "${repo_name}" | jq -r 'last(.[] | select(. and test("(\\d+\\.){2}\\d+"))) ')
  echo "$latest_image_tag"
}

function fetch_helm_chart() {
  if [ $# -lt 3 ]; then
    echo "Usage: fetch_helm_chart <chart> <registry_host> <repository>"
    exit 1
  fi

  local chart_name registry_host repository
  chart_name="$1"; shift
  registry_host="$1"; shift
  repository="$1"

  mkdir -p "${helm_dir}/charts"
  target="${registry_host}/${repository}:${chart_name}"

  export HELM_EXPERIMENTAL_OCI=1
  aws ecr get-login-password | helm registry login --username AWS --password-stdin "${registry_host}"

  printf "==> pulling helm chart %s from ECR\n" "$target"

  helm chart pull "$target"
  helm chart export "$target" --destination "${helm_dir}/charts"
}

function upgrade_helm_chart() {
  if [ $# -lt 3 ]; then
    echo "Usage: upgrade_helm_chart <release_name> <chart> <namespace> [<remaining args>]"
    exit 1
  fi

  local release_name chart version namespace
  release_name=$1; shift
  chart=$1; shift
  namespace=$1; shift
  helm_args="$*"

  echo "Deploying helm chart ${chart} to namespace ${namespace}"
  # shellcheck disable=SC2086
  helm upgrade --install "${release_name}" "${chart}" \
    --history-max 3 \
    --namespace "${namespace}" \
    --values "${helm_dir}/values/${ENVIRONMENT_SHORT}/values.yaml" $helm_args
}

# common vars used in other scripts
scripts_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
root_dir="$(dirname "${scripts_dir}")"
# shellcheck disable=SC2034
application_dir="${root_dir}/application"
version_file="${root_dir}/PIPELINE_VERSION"
# shellcheck disable=SC2034
helm_dir="${root_dir}/helm"
project_name=$(basename "$root_dir" | tr '[:upper:]' '[:lower:]')
echo "Project name: ${project_name}"
