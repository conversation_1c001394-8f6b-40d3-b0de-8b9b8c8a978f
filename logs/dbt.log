[0m17:03:11.436197 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x107ba8850>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x10a802d10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x10a803430>]}


============================== 17:03:11.446964 | f73fea84-1de7-497c-b2f7-c0ea82ff1206 ==============================
[0m17:03:11.446964 [info ] [MainThread]: Running with dbt=1.8.0
[0m17:03:11.447400 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/Users/<USER>/.dbt', 'debug': 'False', 'version_check': 'True', 'log_path': 'logs', 'fail_fast': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt debug', 'introspect': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m17:03:11.595421 [info ] [MainThread]: dbt version: 1.8.0
[0m17:03:11.596202 [info ] [MainThread]: python version: 3.10.12
[0m17:03:11.596651 [info ] [MainThread]: python path: /opt/miniconda3/envs/datalake-airflow-test/bin/python
[0m17:03:11.597063 [info ] [MainThread]: os info: macOS-15.5-arm64-arm-64bit
[0m17:03:12.167600 [info ] [MainThread]: Using profiles dir at /Users/<USER>/.dbt
[0m17:03:12.168261 [info ] [MainThread]: Using profiles.yml file at /Users/<USER>/.dbt/profiles.yml
[0m17:03:12.168606 [info ] [MainThread]: Using dbt_project.yml file at /Users/<USER>/repos/price-crawler/dbt_project.yml
[0m17:03:12.178688 [info ] [MainThread]: adapter type: postgres
[0m17:03:12.179079 [info ] [MainThread]: adapter version: 1.8.0
[0m17:03:12.179434 [info ] [MainThread]: Configuration:
[0m17:03:12.179764 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m17:03:12.180088 [info ] [MainThread]:   dbt_project.yml file [[31mERROR not found[0m]
[0m17:03:12.180408 [info ] [MainThread]: Required dependencies:
[0m17:03:12.180805 [debug] [MainThread]: Executing "git --help"
[0m17:03:12.210368 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--bare]\n           [--git-dir=<path>] [--work-tree=<path>] [--namespace=<name>]\n           [--super-prefix=<path>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone     Clone a repository into a new directory\n   init      Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add       Add file contents to the index\n   mv        Move or rename a file, a directory, or a symlink\n   restore   Restore working tree files\n   rm        Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect    Use binary search to find the commit that introduced a bug\n   diff      Show changes between commits, commit and working tree, etc\n   grep      Print lines matching a pattern\n   log       Show commit logs\n   show      Show various types of objects\n   status    Show the working tree status\n\ngrow, mark and tweak your common history\n   branch    List, create, or delete branches\n   commit    Record changes to the repository\n   merge     Join two or more development histories together\n   rebase    Reapply commits on top of another base tip\n   reset     Reset current HEAD to the specified state\n   switch    Switch branches\n   tag       Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch     Download objects and refs from another repository\n   pull      Fetch from and integrate with another repository or a local branch\n   push      Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m17:03:12.211263 [debug] [MainThread]: STDERR: "b''"
[0m17:03:12.211618 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m17:03:12.211940 [info ] [MainThread]: Connection:
[0m17:03:12.212256 [info ] [MainThread]:   host: localhost
[0m17:03:12.212516 [info ] [MainThread]:   port: 5432
[0m17:03:12.212765 [info ] [MainThread]:   user: dinesh_nallabothula
[0m17:03:12.212999 [info ] [MainThread]:   database: analytical
[0m17:03:12.213228 [info ] [MainThread]:   schema: information_model
[0m17:03:12.213454 [info ] [MainThread]:   connect_timeout: 10
[0m17:03:12.213689 [info ] [MainThread]:   role: None
[0m17:03:12.213919 [info ] [MainThread]:   search_path: None
[0m17:03:12.214150 [info ] [MainThread]:   keepalives_idle: 0
[0m17:03:12.214374 [info ] [MainThread]:   sslmode: None
[0m17:03:12.214592 [info ] [MainThread]:   sslcert: None
[0m17:03:12.214808 [info ] [MainThread]:   sslkey: None
[0m17:03:12.215030 [info ] [MainThread]:   sslrootcert: None
[0m17:03:12.215251 [info ] [MainThread]:   application_name: dbt
[0m17:03:12.215471 [info ] [MainThread]:   retries: 1
[0m17:03:12.215997 [info ] [MainThread]: Registered adapter: postgres=1.8.0
[0m17:03:12.472747 [debug] [MainThread]: Acquiring new postgres connection 'debug'
[0m17:03:14.010269 [debug] [MainThread]: Using postgres connection "debug"
[0m17:03:14.010833 [debug] [MainThread]: On debug: select 1 as id
[0m17:03:14.011222 [debug] [MainThread]: Opening a new connection, currently in state init
[0m17:03:14.033669 [debug] [MainThread]: Postgres adapter: Got a retryable error when attempting to open a postgres connection.
1 attempts remaining. Retrying in 0 seconds.
Error:
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

[0m17:03:14.037947 [debug] [MainThread]: Postgres adapter: Error running SQL: select 1 as id
[0m17:03:14.038430 [debug] [MainThread]: Postgres adapter: Rolling back transaction.
[0m17:03:14.038800 [debug] [MainThread]: On debug: No close available on handle
[0m17:03:14.039172 [info ] [MainThread]:   Connection test: [[31mERROR[0m]

[0m17:03:14.039603 [info ] [MainThread]: [31m2 checks failed:[0m
[0m17:03:14.039922 [info ] [MainThread]: Project loading failed for the following reason:
 project path </Users/<USER>/repos/price-crawler/dbt_project.yml> not found

[0m17:03:14.040244 [info ] [MainThread]: dbt was unable to connect to the specified database.
The database returned the following error:

  >Database Error
  connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
  	Is the server running on that host and accepting TCP/IP connections?
  

Check your database credentials and try again. For more information, visit:
https://docs.getdbt.com/docs/configure-your-profile


[0m17:03:14.107684 [debug] [MainThread]: Resource report: {"command_name": "debug", "command_wall_clock_time": 2.8194757, "process_user_time": 1.68696, "process_kernel_time": 1.000757, "process_mem_max_rss": "122339328", "command_success": false, "process_in_blocks": "0", "process_out_blocks": "0"}
[0m17:03:14.108656 [debug] [MainThread]: Command `dbt debug` failed at 17:03:14.108521 after 2.82 seconds
[0m17:03:14.109214 [debug] [MainThread]: Connection 'debug' was properly closed.
[0m17:03:14.109828 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x107ba8850>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x10a8906d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x10acaafe0>]}
[0m17:03:14.110585 [debug] [MainThread]: Flushing usage events
